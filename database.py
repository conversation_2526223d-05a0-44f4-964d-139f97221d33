from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import text
import datetime

# إنشاء محرك قاعدة البيانات مع تحسينات الأداء
engine = create_engine(
    'sqlite:///accounting.db',
    echo=False,
    connect_args={'check_same_thread': False, 'timeout': 30},  # زيادة مهلة الاتصال وتمكين الوصول متعدد المؤشرات
    pool_pre_ping=True,  # التحقق من صحة الاتصال قبل استخدامه
    pool_recycle=3600,  # إعادة تدوير الاتصالات كل ساعة
    pool_size=20,  # زيادة حجم تجمع الاتصالات
    max_overflow=10  # السماح بتجاوز حجم التجمع عند الحاجة
)

# تحسين أداء SQLAlchemy
from sqlalchemy import event
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """تعيين إعدادات SQLite لتحسين الأداء"""
    cursor = dbapi_connection.cursor()
    cursor.execute("PRAGMA journal_mode=WAL")  # استخدام وضع WAL لتحسين الأداء
    cursor.execute("PRAGMA synchronous=NORMAL")  # تقليل التزامن مع القرص
    cursor.execute("PRAGMA cache_size=10000")  # زيادة حجم ذاكرة التخزين المؤقت
    cursor.execute("PRAGMA temp_store=MEMORY")  # تخزين الجداول المؤقتة في الذاكرة
    cursor.execute("PRAGMA mmap_size=30000000000")  # استخدام تعيين الذاكرة للملفات الكبيرة
    cursor.execute("PRAGMA busy_timeout=30000")  # تعيين مهلة الانتظار إلى 30 ثانية
    cursor.close()

Base = declarative_base()
Session = sessionmaker(bind=engine)

# نموذج العملاء
class Client(Base):
    __tablename__ = 'clients'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20))  # رقم الهاتف الرئيسي (للتوافق مع الإصدارات السابقة)
    email = Column(String(100))
    address = Column(String(200))
    balance = Column(Float, default=0.0)  # المبلغ المستحق (موجب: للعميل، سالب: على العميل)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # العلاقات
    invoices = relationship("Invoice", back_populates="client")
    projects = relationship("Project", back_populates="client")
    documents = relationship("Document", back_populates="client")
    phones = relationship("ClientPhone", back_populates="client", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Client(id={self.id}, name='{self.name}', balance={self.balance})>"

# نموذج أرقام هواتف العملاء
class ClientPhone(Base):
    __tablename__ = 'client_phones'

    id = Column(Integer, primary_key=True)
    client_id = Column(Integer, ForeignKey('clients.id'), nullable=False)
    phone_number = Column(String(20), nullable=False)
    label = Column(String(50))  # وصف الرقم (مثل: شخصي، عمل، منزل)
    is_primary = Column(Boolean, default=False)  # هل هو الرقم الرئيسي

    # العلاقات
    client = relationship("Client", back_populates="phones")

    def __repr__(self):
        return f"<ClientPhone(id={self.id}, client_id={self.client_id}, phone_number='{self.phone_number}')>"

# نموذج الموردين
class Supplier(Base):
    __tablename__ = 'suppliers'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20))  # رقم الهاتف الرئيسي (للتوافق مع الإصدارات السابقة)
    email = Column(String(100))
    address = Column(String(200))
    balance = Column(Float, default=0.0)  # المبلغ المستحق (موجب: للمورد، سالب: على المورد)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # العلاقات
    expenses = relationship("Expense", back_populates="supplier")
    inventory_items = relationship("Inventory", back_populates="supplier")
    documents = relationship("Document", back_populates="supplier")
    phones = relationship("SupplierPhone", back_populates="supplier", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Supplier(id={self.id}, name='{self.name}', balance={self.balance})>"

# نموذج أرقام هواتف الموردين
class SupplierPhone(Base):
    __tablename__ = 'supplier_phones'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    phone_number = Column(String(20), nullable=False)
    label = Column(String(50))  # وصف الرقم (مثل: شخصي، عمل، منزل)
    is_primary = Column(Boolean, default=False)  # هل هو الرقم الرئيسي

    # العلاقات
    supplier = relationship("Supplier", back_populates="phones")

    def __repr__(self):
        return f"<SupplierPhone(id={self.id}, supplier_id={self.supplier_id}, phone_number='{self.phone_number}')>"

# نموذج الموظفين
class Employee(Base):
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    position = Column(String(100))
    phone = Column(String(20))  # رقم الهاتف الرئيسي (للتوافق مع الإصدارات السابقة)
    email = Column(String(100))
    address = Column(String(200))
    hire_date = Column(DateTime, default=datetime.datetime.now)
    salary = Column(Float, default=0.0)
    notes = Column(Text)

    # العلاقات
    salaries = relationship("Salary", back_populates="employee")
    daily_wages = relationship("DailyWage", back_populates="employee")
    phones = relationship("EmployeePhone", back_populates="employee", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Employee(id={self.id}, name='{self.name}')>"

# نموذج أرقام هواتف الموظفين
class EmployeePhone(Base):
    __tablename__ = 'employee_phones'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    phone_number = Column(String(20), nullable=False)
    label = Column(String(50))  # وصف الرقم (مثل: شخصي، عمل، منزل)
    is_primary = Column(Boolean, default=False)  # هل هو الرقم الرئيسي

    # العلاقات
    employee = relationship("Employee", back_populates="phones")

    def __repr__(self):
        return f"<EmployeePhone(id={self.id}, employee_id={self.employee_id}, phone_number='{self.phone_number}')>"

# نموذج الرواتب
class Salary(Base):
    __tablename__ = 'salaries'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'))
    amount = Column(Float, nullable=False)
    payment_date = Column(DateTime, default=datetime.datetime.now)
    month = Column(Integer)
    year = Column(Integer)
    notes = Column(Text)

    # العلاقات
    employee = relationship("Employee", back_populates="salaries")

    def __repr__(self):
        return f"<Salary(id={self.id}, employee_id={self.employee_id}, amount={self.amount})>"

# نموذج اليوميات
class DailyWage(Base):
    __tablename__ = 'daily_wages'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'))
    daily_amount = Column(Float, nullable=False)  # المبلغ اليومي
    work_days = Column(Float, nullable=False)  # عدد أيام العمل
    total_amount = Column(Float, nullable=False)  # إجمالي المبلغ
    advance = Column(Float, default=0)  # السُلف
    net_amount = Column(Float, nullable=False)  # صافي المبلغ
    wage_date = Column(DateTime, default=datetime.datetime.now)  # تاريخ اليومية
    created_at = Column(DateTime, default=datetime.datetime.now)

    # العلاقات
    employee = relationship("Employee", back_populates="daily_wages")

    def __repr__(self):
        return f"<DailyWage(id={self.id}, employee_id={self.employee_id}, daily_amount={self.daily_amount}, work_days={self.work_days})>"

# نموذج المصروفات
class Expense(Base):
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)
    category = Column(String(100))
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    notes = Column(Text)

    # العلاقات
    supplier = relationship("Supplier", back_populates="expenses")

    def __repr__(self):
        return f"<Expense(id={self.id}, title='{self.title}', amount={self.amount})>"

# نموذج الإيرادات
class Revenue(Base):
    __tablename__ = 'revenues'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)
    category = Column(String(100))
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=True)
    notes = Column(Text)

    # العلاقات
    invoice = relationship("Invoice", back_populates="revenues")

    def __repr__(self):
        return f"<Revenue(id={self.id}, title='{self.title}', amount={self.amount})>"

# نموذج الفواتير
class Invoice(Base):
    __tablename__ = 'invoices'

    id = Column(Integer, primary_key=True)
    invoice_number = Column(String(50), unique=True)
    client_id = Column(Integer, ForeignKey('clients.id'))
    date = Column(DateTime, default=datetime.datetime.now)
    due_date = Column(DateTime)
    total_amount = Column(Float, default=0.0)
    paid_amount = Column(Float, default=0.0)
    status = Column(String(20), default='pending')  # pending, paid, partially_paid, cancelled
    notes = Column(Text)

    # العلاقات
    client = relationship("Client", back_populates="invoices")
    items = relationship("InvoiceItem", back_populates="invoice", cascade="all, delete-orphan")
    revenues = relationship("Revenue", back_populates="invoice")

    def __repr__(self):
        return f"<Invoice(id={self.id}, invoice_number='{self.invoice_number}', total_amount={self.total_amount})>"

# نموذج عناصر الفاتورة
class InvoiceItem(Base):
    __tablename__ = 'invoice_items'

    id = Column(Integer, primary_key=True)
    invoice_id = Column(Integer, ForeignKey('invoices.id'))
    description = Column(String(200), nullable=False)
    quantity = Column(Float, default=1.0)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)

    # العلاقات
    invoice = relationship("Invoice", back_populates="items")

    def __repr__(self):
        return f"<InvoiceItem(id={self.id}, description='{self.description}', total_price={self.total_price})>"

# نموذج الإشعارات
class Notification(Base):
    __tablename__ = 'notifications'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    message = Column(Text, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)
    is_read = Column(Boolean, default=False)
    type = Column(String(50))  # invoice_due, low_stock, etc.
    related_id = Column(Integer)  # ID of the related entity (invoice, etc.)

    def __repr__(self):
        return f"<Notification(id={self.id}, title='{self.title}', is_read={self.is_read})>"

# نموذج التنبيهات (للتذكير)
class Reminder(Base):
    __tablename__ = 'reminders'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    description = Column(Text)
    reminder_date = Column(DateTime, nullable=False)  # تاريخ ووقت التنبيه
    created_date = Column(DateTime, default=datetime.datetime.now)
    is_completed = Column(Boolean, default=False)  # هل تم إكمال المهمة
    priority = Column(String(20), default='medium')  # high, medium, low
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)  # المستخدم المسؤول

    # العلاقات
    user = relationship("User")
    events = relationship("Event", back_populates="reminder", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Reminder(id={self.id}, title='{self.title}', reminder_date='{self.reminder_date}')>"

# نموذج الأحداث المرتبطة بالتنبيهات
class Event(Base):
    __tablename__ = 'events'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    description = Column(Text)
    event_date = Column(DateTime, nullable=False)  # تاريخ ووقت الحدث
    created_date = Column(DateTime, default=datetime.datetime.now)
    reminder_id = Column(Integer, ForeignKey('reminders.id'), nullable=False)  # التنبيه المرتبط
    status = Column(String(20), default='pending')  # pending, completed, cancelled
    notes = Column(Text)

    # العلاقات
    reminder = relationship("Reminder", back_populates="events")

    def __repr__(self):
        return f"<Event(id={self.id}, title='{self.title}', event_date='{self.event_date}')>"

# نموذج المشاريع
class Project(Base):
    __tablename__ = 'projects'

    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)
    client_id = Column(Integer, ForeignKey('clients.id'))
    location = Column(String(200))
    area = Column(Float)  # المساحة بالمتر المربع
    start_date = Column(DateTime, default=datetime.datetime.now)
    expected_end_date = Column(DateTime)
    actual_end_date = Column(DateTime)
    status = Column(String(50), default='planning')  # planning, in_progress, completed, cancelled
    budget = Column(Float, default=0.0)
    total_cost = Column(Float, default=0.0)
    description = Column(Text)
    notes = Column(Text)

    # العلاقات
    client = relationship("Client")
    expenses = relationship("ProjectExpense", back_populates="project", cascade="all, delete-orphan")
    materials = relationship("ProjectMaterial", back_populates="project", cascade="all, delete-orphan")
    documents = relationship("Document", back_populates="project", cascade="all, delete-orphan")
    properties = relationship("Property", back_populates="project", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status}')>"

# نموذج الأراضي والشقق
class Property(Base):
    __tablename__ = 'properties'

    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    type = Column(String(50), nullable=False)  # أرض، شقة، فيلا، إلخ
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=True)  # جعل الحقل اختياريًا
    location = Column(String(200))
    area = Column(Float)  # المساحة بالمتر المربع
    price = Column(Float, default=0.0)  # السعر
    status = Column(String(50), default='available')  # available, sold, reserved
    description = Column(Text)
    features = Column(Text)  # مميزات العقار
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # العلاقات
    project = relationship("Project", back_populates="properties")
    documents = relationship("PropertyDocument", back_populates="property", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Property(id={self.id}, title='{self.title}', type='{self.type}', price={self.price})>"

# نموذج وثائق وصور العقارات
class PropertyDocument(Base):
    __tablename__ = 'property_documents'

    id = Column(Integer, primary_key=True)
    property_id = Column(Integer, ForeignKey('properties.id'), nullable=False)
    title = Column(String(200), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(50))  # image, pdf, doc, etc.
    is_main_image = Column(Boolean, default=False)  # هل هي الصورة الرئيسية للعقار
    upload_date = Column(DateTime, default=datetime.datetime.now)
    description = Column(Text)

    # العلاقات
    property = relationship("Property", back_populates="documents")

    def __repr__(self):
        return f"<PropertyDocument(id={self.id}, title='{self.title}', property_id={self.property_id})>"

# نموذج مصروفات المشروع
class ProjectExpense(Base):
    __tablename__ = 'project_expenses'

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('projects.id'))
    expense_id = Column(Integer, ForeignKey('expenses.id'))
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)
    notes = Column(Text)

    # العلاقات
    project = relationship("Project", back_populates="expenses")
    expense = relationship("Expense")

    def __repr__(self):
        return f"<ProjectExpense(id={self.id}, project_id={self.project_id}, amount={self.amount})>"

# نموذج المخزون
class Inventory(Base):
    __tablename__ = 'inventory'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    category = Column(String(100))
    unit = Column(String(50))  # وحدة القياس (متر، كيلوجرام، قطعة، إلخ)
    quantity = Column(Float, default=0.0)
    min_quantity = Column(Float, default=0.0)  # الحد الأدنى للمخزون
    cost_price = Column(Float, default=0.0)
    selling_price = Column(Float, default=0.0)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    location = Column(String(100))  # موقع التخزين
    notes = Column(Text)
    last_updated = Column(DateTime, default=datetime.datetime.now)

    # العلاقات
    supplier = relationship("Supplier", back_populates="inventory_items")
    project_materials = relationship("ProjectMaterial", back_populates="material")

    def __repr__(self):
        return f"<Inventory(id={self.id}, name='{self.name}', quantity={self.quantity})>"

# نموذج المشتريات
class Purchase(Base):
    __tablename__ = 'purchases'

    id = Column(Integer, primary_key=True)
    purchase_number = Column(String(50), unique=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))
    date = Column(DateTime, default=datetime.datetime.now)
    total_amount = Column(Float, default=0.0)
    paid_amount = Column(Float, default=0.0)
    status = Column(String(20), default='pending')  # pending, received, completed, cancelled
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    items = relationship("PurchaseItem", back_populates="purchase", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Purchase(id={self.id}, purchase_number='{self.purchase_number}', total_amount={self.total_amount})>"

# نموذج عناصر المشتريات
class PurchaseItem(Base):
    __tablename__ = 'purchase_items'

    id = Column(Integer, primary_key=True)
    purchase_id = Column(Integer, ForeignKey('purchases.id'))
    inventory_id = Column(Integer, ForeignKey('inventory.id'))
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)
    received_quantity = Column(Float, default=0.0)  # الكمية المستلمة فعلياً
    notes = Column(Text)

    # العلاقات
    purchase = relationship("Purchase", back_populates="items")
    inventory_item = relationship("Inventory")

    def __repr__(self):
        return f"<PurchaseItem(id={self.id}, purchase_id={self.purchase_id}, inventory_id={self.inventory_id}, quantity={self.quantity})>"

# نموذج المبيعات
class Sale(Base):
    __tablename__ = 'sales'

    id = Column(Integer, primary_key=True)
    sale_number = Column(String(50), unique=True)
    client_id = Column(Integer, ForeignKey('clients.id'))
    date = Column(DateTime, default=datetime.datetime.now)
    total_amount = Column(Float, default=0.0)
    paid_amount = Column(Float, default=0.0)
    discount_amount = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    status = Column(String(20), default='pending')  # pending, completed, cancelled, returned
    payment_method = Column(String(50), default='cash')  # cash, credit, bank_transfer
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # العلاقات
    client = relationship("Client")
    items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Sale(id={self.id}, sale_number='{self.sale_number}', total_amount={self.total_amount})>"

# نموذج عناصر المبيعات
class SaleItem(Base):
    __tablename__ = 'sale_items'

    id = Column(Integer, primary_key=True)
    sale_id = Column(Integer, ForeignKey('sales.id'))
    inventory_id = Column(Integer, ForeignKey('inventory.id'))
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)
    discount_amount = Column(Float, default=0.0)
    notes = Column(Text)

    # العلاقات
    sale = relationship("Sale", back_populates="items")
    inventory_item = relationship("Inventory")

    def __repr__(self):
        return f"<SaleItem(id={self.id}, sale_id={self.sale_id}, inventory_id={self.inventory_id}, quantity={self.quantity})>"

# نموذج المواد المستخدمة في المشروع
class ProjectMaterial(Base):
    __tablename__ = 'project_materials'

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('projects.id'))
    material_id = Column(Integer, ForeignKey('inventory.id'))
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)
    date_used = Column(DateTime, default=datetime.datetime.now)
    notes = Column(Text)

    # العلاقات
    project = relationship("Project", back_populates="materials")
    material = relationship("Inventory", back_populates="project_materials")

    def __repr__(self):
        return f"<ProjectMaterial(id={self.id}, project_id={self.project_id}, material_id={self.material_id}, quantity={self.quantity})>"

# نموذج الوثائق والصور
class Document(Base):
    __tablename__ = 'documents'

    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(50))  # image, pdf, doc, etc.
    upload_date = Column(DateTime, default=datetime.datetime.now)
    description = Column(Text)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=True)
    client_id = Column(Integer, ForeignKey('clients.id'), nullable=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)

    # العلاقات
    project = relationship("Project", back_populates="documents")
    client = relationship("Client", back_populates="documents")
    supplier = relationship("Supplier", back_populates="documents")

    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}', file_type='{self.file_type}')>"

# نموذج الإعدادات
class Setting(Base):
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(String(500))
    category = Column(String(100))  # general, appearance, financial, etc.
    description = Column(String(200))

    def __repr__(self):
        return f"<Setting(key='{self.key}', value='{self.value}')>"

# نموذج المستخدمين
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password = Column(String(100), nullable=False)  # سيتم تخزين كلمة المرور بشكل مشفر
    full_name = Column(String(100))
    email = Column(String(100))
    phone = Column(String(20))
    role = Column(String(20), default='user')  # admin, manager, accountant, user, etc.
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # صلاحيات المستخدم
    can_view_dashboard = Column(Boolean, default=True)
    can_manage_clients = Column(Boolean, default=False)
    can_manage_suppliers = Column(Boolean, default=False)
    can_manage_employees = Column(Boolean, default=False)
    can_manage_projects = Column(Boolean, default=False)
    can_manage_inventory = Column(Boolean, default=False)
    can_manage_expenses = Column(Boolean, default=False)
    can_manage_revenues = Column(Boolean, default=False)
    can_manage_invoices = Column(Boolean, default=False)
    can_view_reports = Column(Boolean, default=False)
    can_manage_settings = Column(Boolean, default=False)
    can_manage_users = Column(Boolean, default=False)

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"

    def set_permissions_by_role(self):
        """تعيين الصلاحيات بناءً على دور المستخدم"""
        if self.role == 'admin':
            # المدير لديه جميع الصلاحيات
            self.can_view_dashboard = True
            self.can_manage_clients = True
            self.can_manage_suppliers = True
            self.can_manage_employees = True
            self.can_manage_projects = True
            self.can_manage_inventory = True
            self.can_manage_expenses = True
            self.can_manage_revenues = True
            self.can_manage_invoices = True
            self.can_view_reports = True
            self.can_manage_settings = True
            self.can_manage_users = True
        elif self.role == 'manager':
            # المدير التنفيذي لديه معظم الصلاحيات ما عدا إدارة المستخدمين والإعدادات
            self.can_view_dashboard = True
            self.can_manage_clients = True
            self.can_manage_suppliers = True
            self.can_manage_employees = True
            self.can_manage_projects = True
            self.can_manage_inventory = True
            self.can_manage_expenses = True
            self.can_manage_revenues = True
            self.can_manage_invoices = True
            self.can_view_reports = True
            self.can_manage_settings = False
            self.can_manage_users = False
        elif self.role == 'accountant':
            # المحاسب لديه صلاحيات محدودة تتعلق بالمالية
            self.can_view_dashboard = True
            self.can_manage_clients = True
            self.can_manage_suppliers = True
            self.can_manage_employees = False
            self.can_manage_projects = False
            self.can_manage_inventory = False
            self.can_manage_expenses = True
            self.can_manage_revenues = True
            self.can_manage_invoices = True
            self.can_view_reports = True
            self.can_manage_settings = False
            self.can_manage_users = False
        else:  # user
            # المستخدم العادي لديه صلاحيات محدودة جداً
            self.can_view_dashboard = True
            self.can_manage_clients = False
            self.can_manage_suppliers = False
            self.can_manage_employees = False
            self.can_manage_projects = False
            self.can_manage_inventory = False
            self.can_manage_expenses = False
            self.can_manage_revenues = False
            self.can_manage_invoices = False
            self.can_view_reports = False
            self.can_manage_settings = False
            self.can_manage_users = False

# إنشاء جميع الجداول في قاعدة البيانات
def init_db():
    Base.metadata.create_all(engine)

    # إضافة الإعدادات الافتراضية إذا لم تكن موجودة
    session = Session()

    default_settings = [
        # إعدادات عامة
        {'key': 'company_name', 'value': 'Smart Finish', 'category': 'general', 'description': 'اسم الشركة'},
        {'key': 'company_phone', 'value': '', 'category': 'general', 'description': 'رقم هاتف الشركة'},
        {'key': 'company_email', 'value': '', 'category': 'general', 'description': 'البريد الإلكتروني للشركة'},
        {'key': 'company_address', 'value': '', 'category': 'general', 'description': 'عنوان الشركة'},
        {'key': 'company_logo', 'value': 'resources/logo.png', 'category': 'general', 'description': 'شعار الشركة'},
        {'key': 'company_subtitle', 'value': 'إدارة / محمد جمال', 'category': 'general', 'description': 'العنوان الجانبي للشركة'},

        # إعدادات مالية
        {'key': 'currency', 'value': 'جنية', 'category': 'financial', 'description': 'العملة'},
        {'key': 'tax_rate', 'value': '15', 'category': 'financial', 'description': 'نسبة الضريبة (%)'},
        {'key': 'invoice_prefix', 'value': 'INV-', 'category': 'financial', 'description': 'بادئة رقم الفاتورة'},
        {'key': 'invoice_due_days', 'value': '30', 'category': 'financial', 'description': 'عدد أيام استحقاق الفاتورة الافتراضي'},

        # إعدادات الإشعارات
        {'key': 'enable_notifications', 'value': 'true', 'category': 'notifications', 'description': 'تفعيل الإشعارات'},
        {'key': 'notify_low_stock', 'value': 'true', 'category': 'notifications', 'description': 'إشعار عند انخفاض المخزون'},
        {'key': 'notify_due_invoices', 'value': 'true', 'category': 'notifications', 'description': 'إشعار عند استحقاق الفواتير'},

        # إعدادات النسخ الاحتياطي
        {'key': 'auto_backup', 'value': 'false', 'category': 'backup', 'description': 'نسخ احتياطي تلقائي'},
        {'key': 'backup_frequency', 'value': 'weekly', 'category': 'backup', 'description': 'تكرار النسخ الاحتياطي (daily/weekly/monthly)'},
        {'key': 'backup_path', 'value': 'backups', 'category': 'backup', 'description': 'مسار النسخ الاحتياطي'}
    ]

    for setting_data in default_settings:
        # التحقق مما إذا كان الإعداد موجودًا بالفعل
        existing = session.query(Setting).filter_by(key=setting_data['key']).first()
        if not existing:
            setting = Setting(**setting_data)
            session.add(setting)

    try:
        session.commit()

        # إضافة مستخدم افتراضي إذا لم يكن هناك مستخدمين
        user_count = session.query(User).count()
        if user_count == 0:
            # إنشاء مستخدم افتراضي (admin/admin)
            default_admin = User(
                username="admin",
                password=hash_password("admin"),
                full_name="مدير النظام",
                role="admin",
                is_active=True
            )
            session.add(default_admin)
            session.commit()
            # تم إنشاء مستخدم افتراضي بصمت
    except Exception as e:
        session.rollback()
        # خطأ في تهيئة قاعدة البيانات (تم إخفاء الرسالة)
    finally:
        session.close()

# الحصول على جلسة قاعدة البيانات
def get_session():
    """إنشاء جلسة جديدة مع تحسينات الأداء"""
    session = Session()
    # تعيين مهلة انتهاء الجلسة لتجنب مشاكل التجمد
    session.execute(text("PRAGMA busy_timeout=30000"))  # 30 ثانية
    return session

# الحصول على قيمة إعداد معين
def get_setting(session, key, default=None):
    setting = session.query(Setting).filter_by(key=key).first()
    if setting:
        return setting.value
    return default

# تشفير كلمة المرور
def hash_password(password):
    """تشفير كلمة المرور باستخدام خوارزمية bcrypt"""
    import hashlib
    # استخدام SHA-256 لتشفير كلمة المرور (في الإنتاج يفضل استخدام bcrypt)
    return hashlib.sha256(password.encode()).hexdigest()

# التحقق من صحة كلمة المرور
def verify_password(stored_password, provided_password):
    """التحقق من صحة كلمة المرور"""
    import hashlib
    hashed_provided = hashlib.sha256(provided_password.encode()).hexdigest()
    return stored_password == hashed_provided

# إضافة مستخدم جديد
def add_user(session, username, password, full_name='', email='', phone='', role='user'):
    """إضافة مستخدم جديد إلى قاعدة البيانات"""
    # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    existing_user = session.query(User).filter_by(username=username).first()
    if existing_user:
        return False, "اسم المستخدم موجود بالفعل"

    # تشفير كلمة المرور
    hashed_password = hash_password(password)

    # إنشاء مستخدم جديد
    user = User(
        username=username,
        password=hashed_password,
        full_name=full_name,
        email=email,
        phone=phone,
        role=role
    )

    # تعيين الصلاحيات بناءً على الدور
    user.set_permissions_by_role()

    # إضافة المستخدم إلى قاعدة البيانات
    try:
        session.add(user)
        session.commit()
        return True, "تم إضافة المستخدم بنجاح"
    except Exception as e:
        session.rollback()
        return False, f"حدث خطأ أثناء إضافة المستخدم: {str(e)}"

# التحقق من صحة بيانات الدخول
def authenticate_user(session, username, password):
    """التحقق من صحة بيانات الدخول"""
    # البحث عن المستخدم
    user = session.query(User).filter_by(username=username).first()
    if not user:
        return None, "اسم المستخدم غير موجود"

    # التحقق من كلمة المرور
    if not verify_password(user.password, password):
        return None, "كلمة المرور غير صحيحة"

    # التحقق من حالة المستخدم
    if not user.is_active:
        return None, "الحساب غير نشط"

    # تحديث وقت آخر تسجيل دخول
    user.last_login = datetime.datetime.now()
    session.commit()

    return user, "تم تسجيل الدخول بنجاح"

# تغيير كلمة المرور
def change_password(session, user_id, old_password, new_password):
    """تغيير كلمة المرور للمستخدم"""
    # البحث عن المستخدم
    user = session.query(User).filter_by(id=user_id).first()
    if not user:
        return False, "المستخدم غير موجود"

    # التحقق من كلمة المرور القديمة
    if not verify_password(user.password, old_password):
        return False, "كلمة المرور القديمة غير صحيحة"

    # تشفير كلمة المرور الجديدة
    hashed_password = hash_password(new_password)

    # تحديث كلمة المرور
    try:
        user.password = hashed_password
        session.commit()
        return True, "تم تغيير كلمة المرور بنجاح"
    except Exception as e:
        session.rollback()
        return False, f"حدث خطأ أثناء تغيير كلمة المرور: {str(e)}"

# تحديث قيمة إعداد معين
def update_setting(session, key, value):
    setting = session.query(Setting).filter_by(key=key).first()
    if setting:
        setting.value = value
        session.commit()
        return True
    return False

# تعيين قيمة إعداد (إنشاء إذا لم يكن موجودًا أو تحديث إذا كان موجودًا)
def set_setting(session, key, value):
    setting = session.query(Setting).filter_by(key=key).first()
    if setting:
        setting.value = value
    else:
        setting = Setting(key=key, value=value)
        session.add(setting)
    session.commit()
    return True

# تحديث رصيد العميل
def update_client_balance(session, client_id, amount, operation='add'):
    """
    تحديث رصيد العميل

    المعاملات:
    - session: جلسة قاعدة البيانات
    - client_id: معرف العميل
    - amount: المبلغ المراد إضافته أو خصمه
    - operation: نوع العملية ('add' للإضافة، 'subtract' للخصم)

    ملاحظة: القيمة الموجبة تعني أن المبلغ للعميل، والقيمة السالبة تعني أن المبلغ على العميل
    """
    client = session.query(Client).filter_by(id=client_id).first()
    if client:
        if operation == 'add':
            client.balance += amount
        elif operation == 'subtract':
            client.balance -= amount
        session.commit()
        return True, client.balance
    return False, 0

# تحديث رصيد المورد
def update_supplier_balance(session, supplier_id, amount, operation='add'):
    """
    تحديث رصيد المورد

    المعاملات:
    - session: جلسة قاعدة البيانات
    - supplier_id: معرف المورد
    - amount: المبلغ المراد إضافته أو خصمه
    - operation: نوع العملية ('add' للإضافة، 'subtract' للخصم)

    ملاحظة: القيمة الموجبة تعني أن المبلغ للمورد، والقيمة السالبة تعني أن المبلغ على المورد
    """
    supplier = session.query(Supplier).filter_by(id=supplier_id).first()
    if supplier:
        if operation == 'add':
            supplier.balance += amount
        elif operation == 'subtract':
            supplier.balance -= amount
        session.commit()
        return True, supplier.balance
    return False, 0
