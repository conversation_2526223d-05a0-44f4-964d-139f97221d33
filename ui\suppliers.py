from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QRadioButton, QButtonGroup,
                            QDateEdit, QCalendarWidget, QApplication, QFileDialog,
                            QListWidget, QListWidgetItem, QSplitter, QMenu, QAction)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon, QFont, QColor

import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, BaseDialog)
from database import Supplier, update_supplier_balance, Expense, SupplierPhone
from utils import show_error_message, show_info_message, show_confirmation_message, is_valid_email, is_valid_phone, format_currency, format_date

class SupplierPhoneDialog(BaseDialog):
    """نافذة حوار لإضافة أو تعديل رقم هاتف مورد"""

    def __init__(self, parent=None, phone=None):
        title = "تعديل رقم الهاتف" if phone else "إضافة رقم هاتف جديد"
        super().__init__(parent, title, "small")
        self.phone = phone
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        title = "تعديل رقم الهاتف" if self.phone else "إضافة رقم هاتف جديد"
        self.setWindowTitle(title)
        self.setFixedSize(400, 200)

        # إنشاء التخطيط الرئيسي
        layout = QFormLayout()

        # حقل رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        layout.addRow("رقم الهاتف:", self.phone_edit)

        # حقل وصف الرقم
        self.label_edit = QLineEdit()
        self.label_edit.setPlaceholderText("مثل: شخصي، عمل، منزل")
        layout.addRow("وصف الرقم:", self.label_edit)

        # خيار الرقم الرئيسي
        self.is_primary = QRadioButton("رقم رئيسي")
        layout.addRow("", self.is_primary)

        # إذا كان في وضع التعديل، املأ البيانات
        if self.phone:
            self.phone_edit.setText(self.phone.phone_number)
            self.label_edit.setText(self.phone.label or "")
            self.is_primary.setChecked(self.phone.is_primary)

        # أزرار الحفظ والإلغاء باستخدام النمط الموحد
        button_layout = self.create_button_layout([
            ('save', '💾 حفظ', 'success', self.accept),
            ('cancel', '❌ إلغاء', 'secondary', self.reject)
        ])

        layout.addRow(button_layout)
        self.setLayout(layout)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = [
            ('phone_number', self.phone_edit, 'رقم الهاتف')
        ]
        errors.extend(self.validate_required_fields(required_fields))

        # التحقق من صحة رقم الهاتف
        phone_number = self.phone_edit.text().strip()
        if phone_number and not is_valid_phone(phone_number):
            errors.append("رقم الهاتف غير صحيح")

        return errors

    def get_data(self):
        """الحصول على بيانات رقم الهاتف من النموذج"""
        phone_number = self.phone_edit.text().strip()
        label = self.label_edit.text().strip()
        is_primary = self.is_primary.isChecked()

        return {
            'phone_number': phone_number,
            'label': label,
            'is_primary': is_primary
        }

class BalanceAdjustmentDialog(BaseDialog):
    """نافذة حوار لتعديل المبلغ المستحق للمورد"""

    def __init__(self, parent=None, supplier=None):
        super().__init__(parent, "تعديل المبلغ المستحق للمورد", "medium")
        self.supplier = supplier
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        self.setWindowTitle("تعديل المبلغ المستحق")
        self.setMinimumWidth(400)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عرض معلومات المورد
        supplier_info = QLabel(f"المورد: {self.supplier.name}")
        supplier_info
        main_layout.addWidget(supplier_info)

        current_balance = QLabel(f"المبلغ المستحق الحالي: {self.supplier.balance:.2f}")
        main_layout.addWidget(current_balance)

        # إضافة شرح للمبلغ المستحق
        balance_explanation = QLabel("ملاحظة: القيمة الموجبة تعني أن المبلغ للمورد، والقيمة السالبة تعني أن المبلغ على المورد")
        balance_explanation.setWordWrap(True)
        main_layout.addWidget(balance_explanation)

        # إنشاء مجموعة الخيارات
        operation_group = QGroupBox("نوع العملية")
        operation_layout = QVBoxLayout()

        self.add_radio = QRadioButton("إضافة مبلغ")
        self.add_radio.setChecked(True)
        self.subtract_radio = QRadioButton("خصم مبلغ")
        self.set_radio = QRadioButton("تعيين قيمة جديدة")

        operation_layout.addWidget(self.add_radio)
        operation_layout.addWidget(self.subtract_radio)
        operation_layout.addWidget(self.set_radio)

        operation_group.setLayout(operation_layout)
        main_layout.addWidget(operation_group)

        # إنشاء مجموعة أزرار الاختيار
        self.operation_group = QButtonGroup()
        self.operation_group.addButton(self.add_radio, 1)
        self.operation_group.addButton(self.subtract_radio, 2)
        self.operation_group.addButton(self.set_radio, 3)

        # حقل إدخال المبلغ
        amount_layout = QFormLayout()
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("أدخل المبلغ")
        amount_layout.addRow("المبلغ:", self.amount_edit)
        main_layout.addLayout(amount_layout)

        # أزرار الحفظ والإلغاء باستخدام النمط الموحد
        button_layout = self.create_button_layout([
            ('save', '💾 تطبيق التعديل', 'success', self.accept),
            ('cancel', '❌ إلغاء', 'secondary', self.reject)
        ])

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = [
            ('amount', self.amount_edit, 'المبلغ')
        ]
        errors.extend(self.validate_required_fields(required_fields))

        # التحقق من صحة المبلغ
        amount_text = self.amount_edit.text().strip()
        if amount_text:
            try:
                amount = float(amount_text)
                if amount <= 0:
                    errors.append("المبلغ يجب أن يكون أكبر من الصفر")
            except ValueError:
                errors.append("المبلغ يجب أن يكون رقماً صحيحاً")

        return errors

    def get_data(self):
        """الحصول على بيانات التعديل"""
        amount = float(self.amount_edit.text().strip())
        operation_id = self.operation_group.checkedId()

        if operation_id == 1:
            # إضافة مبلغ
            return {'amount': amount, 'operation': 'add'}
        elif operation_id == 2:
            # خصم مبلغ
            return {'amount': amount, 'operation': 'subtract'}
        elif operation_id == 3:
            # تعيين قيمة جديدة
            current_balance = self.supplier.balance
            # حساب الفرق بين القيمة الحالية والقيمة الجديدة
            if amount > current_balance:
                return {'amount': amount - current_balance, 'operation': 'add'}
            else:
                return {'amount': current_balance - amount, 'operation': 'subtract'}

        return None

class SupplierDialog(BaseDialog):
    """نافذة حوار لإضافة أو تعديل مورد"""

    def __init__(self, parent=None, supplier=None, session=None):
        title = "تعديل بيانات المورد" if supplier else "إضافة مورد جديد"
        super().__init__(parent, title, "xlarge")
        self.supplier = supplier
        self.session = session or (parent.session if parent else None)
        self.phones = []  # قائمة أرقام الهواتف المؤقتة
        self.init_ui()
        self.load_phones()

    def init_ui(self):

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # إنشاء تخطيط مقسم
        splitter = QSplitter(Qt.Horizontal)

        # الجزء الأيمن: معلومات المورد
        supplier_info_widget = QWidget()
        form_layout = QFormLayout()
        form_layout.setContentsMargins(10, 10, 10, 10)
        form_layout.setSpacing(15)

        # تعيين نمط للعناوين (الكلمات على اليمين)
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # تكبير حجم خط العناوين
        label_font = QFont()
        label_font.setPointSize(14)  # زيادة حجم الخط من 12 إلى 14
        label_font.setBold(True)

        # تعيين نمط موحد للحقول
        input_style = """
            QLineEdit {
                padding: 8px;
                font-size: 16px;
                border: 1px solid #ccc;
                border-radius: 4px;
                min-height: 35px;
            }
            QTextEdit {
                padding: 8px;
                font-size: 16px;
                border: 1px solid #ccc;
                border-radius: 4px;
                min-height: 60px;
                max-height: 80px;
            }
        """

        # حقل الاسم
        self.name_edit = QLineEdit()
        if self.supplier:
            self.name_edit.setText(self.supplier.name)
        self.name_edit.setStyleSheet(input_style)
        self.name_edit.setPlaceholderText("أدخل اسم المورد")
        name_label = QLabel("الاسم:")
        name_label.setFont(label_font)
        form_layout.addRow(name_label, self.name_edit)

        # حقل رقم الهاتف الرئيسي (للتوافق مع الإصدارات السابقة)
        self.phone_edit = QLineEdit()
        if self.supplier and self.supplier.phone:
            self.phone_edit.setText(self.supplier.phone)
        self.phone_edit.setStyleSheet(input_style)
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الرئيسي")
        phone_label = QLabel("رقم الهاتف الرئيسي:")
        phone_label.setFont(label_font)
        form_layout.addRow(phone_label, self.phone_edit)

        # حقل البريد الإلكتروني
        self.email_edit = QLineEdit()
        if self.supplier and self.supplier.email:
            self.email_edit.setText(self.supplier.email)
        self.email_edit.setStyleSheet(input_style)
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني")
        email_label = QLabel("البريد الإلكتروني:")
        email_label.setFont(label_font)
        form_layout.addRow(email_label, self.email_edit)

        # حقل العنوان
        self.address_edit = QLineEdit()
        if self.supplier and self.supplier.address:
            self.address_edit.setText(self.supplier.address)
        self.address_edit.setStyleSheet(input_style)
        self.address_edit.setPlaceholderText("أدخل العنوان")
        address_label = QLabel("العنوان:")
        address_label.setFont(label_font)
        form_layout.addRow(address_label, self.address_edit)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        if self.supplier and self.supplier.notes:
            self.notes_edit.setText(self.supplier.notes)
        self.notes_edit.setStyleSheet(input_style)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات إضافية")
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(label_font)
        form_layout.addRow(notes_label, self.notes_edit)

        supplier_info_widget.setLayout(form_layout)

        # الجزء الأيسر: أرقام الهواتف
        phones_widget = QWidget()
        phones_widget.setMaximumWidth(300)  # تحديد العرض الأقصى لقائمة أرقام الهواتف
        phones_layout = QVBoxLayout()
        phones_layout.setContentsMargins(5, 5, 5, 5)
        phones_layout.setSpacing(8)

        # عنوان قائمة أرقام الهواتف
        phones_title = QLabel("أرقام الهواتف:")
        phones_title.setFont(label_font)
        phones_title
        phones_layout.addWidget(phones_title)

        # قائمة أرقام الهواتف
        self.phones_list = QListWidget()
        self.phones_list
        self.phones_list.setAlternatingRowColors(True)
        self.phones_list.setMinimumHeight(250)  # تقليل الارتفاع الأدنى
        phones_layout.addWidget(self.phones_list)

        # أزرار إدارة أرقام الهواتف باستخدام النمط الموحد
        phones_buttons_layout = QVBoxLayout()
        phones_buttons_layout.setSpacing(8)

        # إنشاء الأزرار باستخدام StyledButton
        self.add_phone_button = StyledButton("📞 إضافة رقم", "success", "normal")
        self.add_phone_button.clicked.connect(self.add_phone)

        self.edit_phone_button = StyledButton("✏️ تعديل رقم", "primary", "normal")
        self.edit_phone_button.clicked.connect(self.edit_phone)

        self.delete_phone_button = StyledButton("🗑️ حذف رقم", "danger", "normal")
        self.delete_phone_button.clicked.connect(self.delete_phone)

        self.view_phone_button = StyledButton("👁️ عرض التفاصيل", "info", "normal")
        self.view_phone_button.clicked.connect(self.view_phone_details)

        # إضافة الأزرار بعرض كامل
        phones_buttons_layout.addWidget(self.add_phone_button)
        phones_buttons_layout.addWidget(self.edit_phone_button)
        phones_buttons_layout.addWidget(self.delete_phone_button)
        phones_buttons_layout.addWidget(self.view_phone_button)

        phones_layout.addLayout(phones_buttons_layout)
        phones_widget.setLayout(phones_layout)

        # إضافة الأجزاء إلى المقسم
        splitter.addWidget(supplier_info_widget)
        splitter.addWidget(phones_widget)

        # ضبط أحجام المقسم - تخصيص مساحة متساوية بين حقول البيانات وقائمة أرقام الهواتف
        splitter.setSizes([600, 300])

        main_layout.addWidget(splitter)

        # أزرار الحفظ والإلغاء بعرض كامل وجانب بعض
        main_buttons_layout = QHBoxLayout()
        main_buttons_layout.setSpacing(15)
        main_buttons_layout.setContentsMargins(0, 20, 0, 0)

        # زر الحفظ
        self.save_button = StyledButton("💾 حفظ بيانات المورد", "success", "large")
        self.save_button.clicked.connect(self.accept)
        main_buttons_layout.addWidget(self.save_button.button)

        # زر الإلغاء
        self.cancel_button = StyledButton("❌ إلغاء العملية", "secondary", "large")
        self.cancel_button.clicked.connect(self.reject)
        main_buttons_layout.addWidget(self.cancel_button.button)

        main_layout.addLayout(main_buttons_layout)
        self.setLayout(main_layout)

    def load_phones(self):
        """تحميل أرقام الهواتف من قاعدة البيانات"""
        if self.supplier and self.session:
            try:
                phone_records = self.session.query(SupplierPhone).filter_by(supplier_id=self.supplier.id).all()
                for phone_record in phone_records:
                    phone = SupplierPhone(
                        phone_number=phone_record.phone_number,
                        label=phone_record.label,
                        is_primary=phone_record.is_primary
                    )
                    self.phones.append(phone)
                self.refresh_phones_list()
            except Exception as e:
                print(f"خطأ في تحميل أرقام الهواتف: {str(e)}")

    def refresh_phones_list(self):
        """تحديث قائمة أرقام الهواتف"""
        self.phones_list.clear()

        for phone in self.phones:
            # إنشاء عنصر في القائمة
            label_text = f"{phone.phone_number}"
            if phone.label:
                label_text += f" ({phone.label})"

            item = QListWidgetItem(label_text)

            # تعيين محاذاة النص في المنتصف
            item.setTextAlignment(Qt.AlignCenter)

            # تعيين نمط خاص للرقم الرئيسي
            if phone.is_primary:
                item.setText(f"{label_text} [رئيسي]")
                item.setForeground(QColor("#0078D7"))  # لون أزرق للرقم الرئيسي
                font = item.font()
                font.setBold(True)
                font.setPointSize(17)  # تعيين حجم الخط بشكل صريح
                item.setFont(font)
            else:
                # تعيين حجم الخط للعناصر العادية أيضاً
                font = item.font()
                font.setPointSize(17)
                item.setFont(font)

            # تخزين معرف الهاتف في البيانات المخصصة للعنصر
            if hasattr(phone, 'id') and phone.id:
                item.setData(Qt.UserRole, phone.id)

            # إضافة العنصر إلى القائمة
            self.phones_list.addItem(item)

    def add_phone(self):
        """إضافة رقم هاتف جديد"""
        dialog = SupplierPhoneDialog(self)

        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إضافة رقم الهاتف إلى القائمة المؤقتة
                phone = SupplierPhone(
                    phone_number=data['phone_number'],
                    label=data['label'],
                    is_primary=data['is_primary']
                )

                # إذا كان هذا الرقم رئيسياً، قم بإلغاء تعيين الأرقام الرئيسية الأخرى
                if data['is_primary']:
                    for p in self.phones:
                        p.is_primary = False

                self.phones.append(phone)

                # تحديث القائمة
                self.refresh_phones_list()

                # تحديث حقل رقم الهاتف الرئيسي إذا كان هذا هو الرقم الرئيسي
                if data['is_primary']:
                    self.phone_edit.setText(data['phone_number'])

    def edit_phone(self):
        """تعديل رقم هاتف محدد"""
        current_row = self.phones_list.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة")
            return

        phone = self.phones[current_row]
        dialog = SupplierPhoneDialog(self, phone)

        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات رقم الهاتف
                old_phone_number = phone.phone_number
                phone.phone_number = data['phone_number']
                phone.label = data['label']

                # إذا كان هذا الرقم رئيسياً، قم بإلغاء تعيين الأرقام الرئيسية الأخرى
                if data['is_primary'] and not phone.is_primary:
                    for p in self.phones:
                        p.is_primary = False

                phone.is_primary = data['is_primary']

                # تحديث القائمة
                self.refresh_phones_list()

                # تحديث حقل رقم الهاتف الرئيسي
                if data['is_primary']:
                    self.phone_edit.setText(data['phone_number'])
                elif old_phone_number == self.phone_edit.text():
                    # إذا كان الرقم المعدل هو الرقم الرئيسي الحالي، ابحث عن رقم رئيسي آخر
                    primary_phone = next((p for p in self.phones if p.is_primary), None)
                    self.phone_edit.setText(primary_phone.phone_number if primary_phone else "")

    def view_phone_details(self):
        """عرض تفاصيل رقم الهاتف المحدد"""
        current_row = self.phones_list.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة")
            return

        phone = self.phones[current_row]

        # إنشاء نافذة حوار لعرض التفاصيل
        details_dialog = QDialog(self)
        details_dialog.setWindowTitle("تفاصيل رقم الهاتف")
        details_dialog.setMinimumWidth(400)
        details_dialog.setMinimumHeight(300)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout()

        # إنشاء نموذج للتفاصيل
        form_layout = QFormLayout()

        # تعيين نمط للعناوين
        label_font = QFont()
        label_font.setPointSize(14)
        label_font.setBold(True)

        # رقم الهاتف
        phone_number_label = QLabel("رقم الهاتف:")
        phone_number_label.setFont(label_font)
        phone_number_value = QLabel(phone.phone_number)
        phone_number_value
        form_layout.addRow(phone_number_label, phone_number_value)

        # الوصف
        description_label = QLabel("الوصف:")
        description_label.setFont(label_font)
        description_value = QLabel(phone.label or "غير متوفر")
        description_value
        form_layout.addRow(description_label, description_value)

        # النوع
        type_label = QLabel("النوع:")
        type_label.setFont(label_font)
        type_value = QLabel("رقم رئيسي" if phone.is_primary else "رقم إضافي")
        type_value.setStyleSheet("font-size: 16px; font-weight: bold; color: " +
                                ("#4CAF50" if phone.is_primary else "#FF9800") + ";")
        form_layout.addRow(type_label, type_value)

        # إضافة النموذج إلى التخطيط
        layout.addLayout(form_layout)

        # زر الإغلاق بعرض القائمة
        close_button = QPushButton("🔙 إغلاق")
        close_button
        close_button.clicked.connect(details_dialog.accept)

        layout.addStretch()
        layout.addWidget(close_button)

        details_dialog.setLayout(layout)
        details_dialog.exec_()

    def delete_phone(self):
        """حذف رقم هاتف محدد"""
        current_row = self.phones_list.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة")
            return

        phone = self.phones[current_row]

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف رقم الهاتف {phone.phone_number}؟"):
            # إذا كان الرقم المحذوف هو الرقم الرئيسي، قم بتعيين أول رقم آخر كرئيسي
            was_primary = phone.is_primary
            self.phones.pop(current_row)

            if was_primary and self.phones:
                self.phones[0].is_primary = True
                self.phone_edit.setText(self.phones[0].phone_number)
            elif was_primary:
                self.phone_edit.setText("")

            # تحديث القائمة
            self.refresh_phones_list()

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = [
            ('name', self.name_edit, 'اسم المورد')
        ]
        errors.extend(self.validate_required_fields(required_fields))

        # التحقق من صحة رقم الهاتف
        phone = self.phone_edit.text().strip()
        if phone and not is_valid_phone(phone):
            errors.append("رقم الهاتف غير صحيح")

        # التحقق من صحة البريد الإلكتروني
        email = self.email_edit.text().strip()
        if email and not is_valid_email(email):
            errors.append("البريد الإلكتروني غير صحيح")

        return errors

    def get_data(self):
        """الحصول على بيانات المورد من النموذج"""
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        email = self.email_edit.text().strip()
        address = self.address_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()

        # تعيين رقم الهاتف الرئيسي من قائمة أرقام الهواتف
        primary_phone = None
        for p in self.phones:
            if p.is_primary:
                primary_phone = p.phone_number
                break

        # إذا لم يكن هناك رقم هاتف رئيسي، استخدم الرقم المدخل في حقل رقم الهاتف الرئيسي
        if not primary_phone:
            primary_phone = phone

        return {
            'name': name,
            'phone': primary_phone,  # استخدام رقم الهاتف الرئيسي
            'email': email,
            'address': address,
            'balance': 0.0 if not self.supplier else self.supplier.balance,  # الحفاظ على المبلغ المستحق الحالي
            'notes': notes,
            'phones': self.phones  # إضافة قائمة أرقام الهواتف
        }

class SupplierFinancialDetailsDialog(QDialog):
    """نافذة حوار لإدخال وعرض التفاصيل المالية للمورد"""

    def __init__(self, parent=None, supplier=None, session=None):
        super().__init__(parent)
        self.supplier = supplier
        self.session = session
        self.init_ui()
        self.load_data()

    def init_ui(self):
        # إعداد نافذة الحوار
        self.setWindowTitle(f"التفاصيل المالية للمورد: {self.supplier.name}")
        self.setMinimumWidth(1200)
        self.setMinimumHeight(900)  # زيادة ارتفاع النافذة لاستيعاب المزيد من البيانات
        self.resize(1300, 950)  # تعيين الحجم الافتراضي للنافذة

        # تعيين موضع النافذة في وسط الشاشة
        screen_geometry = QApplication.desktop().availableGeometry()
        window_geometry = self.geometry()
        x = (screen_geometry.width() - window_geometry.width()) // 2
        y = (screen_geometry.height() - window_geometry.height()) // 2
        self.move(x, y)

        # ضبط حجم الخط للنافذة بأكملها
        font = self.font()
        font.setPointSize(12)  # زيادة حجم الخط الافتراضي
        self.setFont(font)

        # إنشاء التخطيط الرئيسي مع هوامش أقل
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)  # زيادة المسافة بين العناصر

        # عرض معلومات المورد
        supplier_info = QLabel(f"المورد: {self.supplier.name}")
        supplier_info
        supplier_info.setAlignment(Qt.AlignCenter)  # توسيط النص
        main_layout.addWidget(supplier_info)

        # إنشاء مجموعة إدخال البيانات المالية
        input_group = QGroupBox("إدخال البيانات المالية")
        input_group

        # استخدام تخطيط الشبكة بدلاً من تخطيط النموذج للتحكم بشكل أفضل في العرض
        input_layout = QVBoxLayout()
        input_layout.setSpacing(20)  # زيادة المسافة بين العناصر
        input_layout.setContentsMargins(20, 30, 20, 20)  # زيادة الهوامش

        # تعيين نمط موحد للحقول
        field_style = "font-size: 16px; font-weight: bold; padding: 8px; border: 1px solid #CCCCCC; border-radius: 5px; background-color: #FFFFFF;"
        label_style = "font-size: 16px; font-weight: bold; color: #333333; qproperty-alignment: 'AlignRight | AlignVCenter';"

        # إنشاء صفوف الإدخال
        # المبلغ المطلوب
        amount_row = QHBoxLayout()
        input_label1 = QLabel("المبلغ المطلوب:")
        input_label1.setStyleSheet(label_style)
        input_label1.setFixedWidth(150)
        self.required_amount_edit = QLineEdit()
        self.required_amount_edit.setPlaceholderText("أدخل المبلغ المطلوب")
        self.required_amount_edit.setStyleSheet(field_style)
        self.required_amount_edit.setFixedHeight(40)
        amount_row.addWidget(input_label1)
        amount_row.addWidget(self.required_amount_edit)
        input_layout.addLayout(amount_row)

        # المبلغ المدفوع
        paid_row = QHBoxLayout()
        input_label2 = QLabel("المبلغ المدفوع:")
        input_label2.setStyleSheet(label_style)
        input_label2.setFixedWidth(150)
        self.paid_amount_edit = QLineEdit()
        self.paid_amount_edit.setPlaceholderText("أدخل المبلغ المدفوع")
        self.paid_amount_edit.setStyleSheet(field_style)
        self.paid_amount_edit.setFixedHeight(40)
        paid_row.addWidget(input_label2)
        paid_row.addWidget(self.paid_amount_edit)
        input_layout.addLayout(paid_row)

        # تاريخ الدفع
        payment_date_row = QHBoxLayout()
        input_label3 = QLabel("تاريخ الدفع:")
        input_label3.setStyleSheet(label_style)
        input_label3.setFixedWidth(150)
        self.payment_date_edit = QDateEdit(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setStyleSheet(field_style)
        self.payment_date_edit.setFixedHeight(40)
        self.payment_date_edit.setDisplayFormat("yyyy-MM-dd")  # تنسيق التاريخ
        payment_date_row.addWidget(input_label3)
        payment_date_row.addWidget(self.payment_date_edit)
        input_layout.addLayout(payment_date_row)

        # تاريخ المبلغ المتبقي
        due_date_row = QHBoxLayout()
        input_label4 = QLabel("تاريخ المبلغ المتبقي:")
        input_label4.setStyleSheet(label_style)
        input_label4.setFixedWidth(150)
        self.due_date_edit = QDateEdit(QDate.currentDate().addDays(30))
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setStyleSheet(field_style)
        self.due_date_edit.setFixedHeight(40)
        self.due_date_edit.setDisplayFormat("yyyy-MM-dd")  # تنسيق التاريخ
        due_date_row.addWidget(input_label4)
        due_date_row.addWidget(self.due_date_edit)
        input_layout.addLayout(due_date_row)

        # تعيين تخطيط المجموعة
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # إنشاء مجموعة ملخص المعلومات المالية
        summary_group = QGroupBox("ملخص المعلومات المالية")
        summary_group

        summary_layout = QVBoxLayout()
        summary_layout.setSpacing(15)
        summary_layout.setContentsMargins(20, 30, 20, 20)

        # تعيين نمط موحد للتسميات والقيم
        summary_label_style = "font-size: 16px; font-weight: bold; color: #333333; qproperty-alignment: 'AlignRight | AlignVCenter';"
        summary_value_style = "font-size: 16px; font-weight: bold; color: #0078D7; background-color: #F0F8FF; border: 1px solid #CCCCCC; border-radius: 5px; padding: 8px;"

        # المبلغ المطلوب (إجمالي المصروفات)
        total_due_row = QHBoxLayout()
        summary_label1 = QLabel("إجمالي المبلغ المطلوب:")
        summary_label1.setStyleSheet(summary_label_style)
        summary_label1.setFixedWidth(180)
        self.total_due_label = QLabel()
        self.total_due_label.setStyleSheet(summary_value_style)
        self.total_due_label.setFixedHeight(40)
        self.total_due_label.setAlignment(Qt.AlignCenter)
        total_due_row.addWidget(summary_label1)
        total_due_row.addWidget(self.total_due_label)
        summary_layout.addLayout(total_due_row)

        # المبلغ المدفوع
        total_paid_row = QHBoxLayout()
        summary_label2 = QLabel("إجمالي المبلغ المدفوع:")
        summary_label2.setStyleSheet(summary_label_style)
        summary_label2.setFixedWidth(180)
        self.total_paid_label = QLabel()
        self.total_paid_label.setStyleSheet(summary_value_style)
        self.total_paid_label.setFixedHeight(40)
        self.total_paid_label.setAlignment(Qt.AlignCenter)
        total_paid_row.addWidget(summary_label2)
        total_paid_row.addWidget(self.total_paid_label)
        summary_layout.addLayout(total_paid_row)

        # المبلغ المتبقي
        remaining_row = QHBoxLayout()
        summary_label3 = QLabel("المبلغ المتبقي:")
        summary_label3.setStyleSheet(summary_label_style)
        summary_label3.setFixedWidth(180)
        self.remaining_label = QLabel()
        self.remaining_label.setStyleSheet(summary_value_style + "color: #FF0000;")  # لون أحمر للمبلغ المتبقي
        self.remaining_label.setFixedHeight(40)
        self.remaining_label.setAlignment(Qt.AlignCenter)
        remaining_row.addWidget(summary_label3)
        remaining_row.addWidget(self.remaining_label)
        summary_layout.addLayout(remaining_row)

        # تعيين تخطيط المجموعة
        summary_group.setLayout(summary_layout)
        main_layout.addWidget(summary_group)

        # إضافة مجموعة الفلترة
        filter_group = QGroupBox("فلترة البيانات")
        filter_group

        # استخدام تخطيط الشبكة للتحكم بشكل أفضل في العرض
        filter_layout = QVBoxLayout()
        filter_layout.setSpacing(15)
        filter_layout.setContentsMargins(20, 30, 20, 20)

        # تعيين نمط موحد للحقول والتسميات
        filter_field_style = "font-size: 14px; font-weight: bold; padding: 5px; border: 1px solid #CCCCCC; border-radius: 5px; background-color: #FFFFFF;"
        filter_label_style = "font-size: 14px; font-weight: bold; color: #333333;"

        # صف التاريخ
        date_filter_row = QHBoxLayout()
        date_filter_row.setSpacing(10)

        # من تاريخ
        from_date_layout = QHBoxLayout()
        from_label = QLabel("من:")
        from_label.setStyleSheet(filter_label_style)
        from_label.setFixedWidth(40)
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setCalendarPopup(True)
        self.from_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.from_date_edit.setStyleSheet(filter_field_style)
        self.from_date_edit.setFixedHeight(35)
        self.from_date_edit.setFixedWidth(150)
        self.from_date_edit.setDisplayFormat("yyyy-MM-dd")
        from_date_layout.addWidget(from_label)
        from_date_layout.addWidget(self.from_date_edit)

        # إلى تاريخ
        to_date_layout = QHBoxLayout()
        to_label = QLabel("إلى:")
        to_label.setStyleSheet(filter_label_style)
        to_label.setFixedWidth(40)
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setCalendarPopup(True)
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setStyleSheet(filter_field_style)
        self.to_date_edit.setFixedHeight(35)
        self.to_date_edit.setFixedWidth(150)
        self.to_date_edit.setDisplayFormat("yyyy-MM-dd")
        to_date_layout.addWidget(to_label)
        to_date_layout.addWidget(self.to_date_edit)

        # الحالة
        status_layout = QHBoxLayout()
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet(filter_label_style)
        status_label.setFixedWidth(50)
        self.status_combo = QComboBox()
        self.status_combo.addItem("الكل", "all")
        self.status_combo.addItem("قيد الانتظار", "pending")
        self.status_combo.addItem("مدفوع", "paid")
        self.status_combo.addItem("مدفوع جزئياً", "partially_paid")
        self.status_combo.addItem("ملغى", "cancelled")
        self.status_combo.setStyleSheet(filter_field_style)
        self.status_combo.setFixedHeight(35)
        self.status_combo.setFixedWidth(150)
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.status_combo)

        # إضافة عناصر التاريخ والحالة إلى صف التاريخ
        date_filter_row.addLayout(from_date_layout)
        date_filter_row.addLayout(to_date_layout)
        date_filter_row.addLayout(status_layout)
        date_filter_row.addStretch(1)  # إضافة مساحة مرنة

        # صف الأزرار
        buttons_row = QHBoxLayout()

        # زر تطبيق الفلترة
        self.apply_filter_button = QPushButton("تطبيق الفلترة")
        self.apply_filter_button.clicked.connect(self.apply_filter)
        self.apply_filter_button.setFixedSize(150, 40)
        self.apply_filter_button

        # زر إعادة تعيين الفلترة
        self.reset_filter_button = QPushButton("إعادة تعيين")
        self.reset_filter_button.clicked.connect(self.reset_filter)
        self.reset_filter_button.setFixedSize(150, 40)
        self.reset_filter_button

        # إضافة الأزرار إلى صف الأزرار
        buttons_row.addStretch(1)  # إضافة مساحة مرنة
        buttons_row.addWidget(self.apply_filter_button)
        buttons_row.addSpacing(20)  # إضافة مسافة بين الأزرار
        buttons_row.addWidget(self.reset_filter_button)
        buttons_row.addStretch(1)  # إضافة مساحة مرنة

        # إضافة الصفوف إلى تخطيط الفلترة
        filter_layout.addLayout(date_filter_row)
        filter_layout.addLayout(buttons_row)

        # تعيين تخطيط المجموعة
        filter_group.setLayout(filter_layout)
        main_layout.addWidget(filter_group)

        # إنشاء جدول المصروفات
        expenses_group = QGroupBox("الفواتير")
        expenses_group
        expenses_layout = QVBoxLayout()
        expenses_layout.setContentsMargins(20, 30, 20, 20)

        # إنشاء الجدول المتطور
        self.expenses_table = StyledTable()
        self.expenses_table.setColumnCount(8)
        self.expenses_table.setHorizontalHeaderLabels(["رقم الفاتورة", "التاريخ", "تاريخ المبلغ المتبقي", "المبلغ الإجمالي", "المبلغ المدفوع", "الحالة", "تعديل", "حذف"])

        # StyledTable يطبق التنسيق المتطور تلقائياً

        # تعيين خصائص الجدول
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setSelectionMode(QTableWidget.SingleSelection)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setMinimumHeight(300)
        self.expenses_table.setAlternatingRowColors(True)  # تلوين الصفوف بالتناوب

        # تعيين حجم الخط للجدول
        table_font = self.expenses_table.font()
        table_font.setPointSize(12)
        self.expenses_table.setFont(table_font)

        # زيادة ارتفاع الصفوف
        self.expenses_table.verticalHeader().setDefaultSectionSize(50)

        # إخفاء رأس العمود الرأسي
        self.expenses_table.verticalHeader().setVisible(False)

        # تنسيق خلايا الجدول
        self.expenses_table

        # إضافة الجدول إلى التخطيط
        expenses_layout.addWidget(self.expenses_table)
        expenses_group.setLayout(expenses_layout)
        main_layout.addWidget(expenses_group)

        # إنشاء أزرار الإجراءات
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 20, 0, 10)

        # زر حفظ التغييرات
        self.save_button = QPushButton("حفظ التغييرات")
        self.save_button.clicked.connect(self.save_changes)
        self.save_button.setFixedSize(200, 50)
        self.save_button

        # زر الإغلاق
        self.close_button = QPushButton("إغلاق")
        self.close_button.clicked.connect(self.accept)
        self.close_button.setFixedSize(200, 50)
        self.close_button

        # إضافة الأزرار إلى التخطيط
        button_layout.addStretch(1)
        button_layout.addWidget(self.save_button)
        button_layout.addSpacing(30)
        button_layout.addWidget(self.close_button)
        button_layout.addStretch(1)

        # إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def add_financial_data(self):
        """إضافة بيانات مالية جديدة"""
        try:
            # التحقق من صحة البيانات المدخلة
            required_amount_text = self.required_amount_edit.text().strip()
            paid_amount_text = self.paid_amount_edit.text().strip()

            if not required_amount_text:
                show_error_message("خطأ", "يجب إدخال المبلغ المطلوب")
                return

            try:
                required_amount = float(required_amount_text)
                paid_amount = float(paid_amount_text) if paid_amount_text else 0.0
            except ValueError:
                show_error_message("خطأ", "يجب إدخال قيم رقمية صحيحة")
                return

            if required_amount <= 0:
                show_error_message("خطأ", "يجب أن يكون المبلغ المطلوب أكبر من صفر")
                return

            if paid_amount < 0:
                show_error_message("خطأ", "لا يمكن أن يكون المبلغ المدفوع سالباً")
                return

            # تحديد حالة المصروف
            if paid_amount == 0:
                status = 'pending'  # قيد الانتظار
            elif paid_amount >= required_amount:
                status = 'paid'  # مدفوع
                paid_amount = required_amount  # تصحيح المبلغ المدفوع إذا كان أكبر من المطلوب
            else:
                status = 'partially_paid'  # مدفوع جزئياً

            # إنشاء رقم فاتورة جديد
            from datetime import datetime
            expense_number = f"INV-{self.supplier.id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # طلب تأكيد الإضافة
            confirmation_message = f"هل أنت متأكد من إضافة الفاتورة التالية؟\n\n"
            confirmation_message += f"المبلغ المطلوب: {required_amount:.2f}\n"
            confirmation_message += f"المبلغ المدفوع: {paid_amount:.2f}\n"
            confirmation_message += f"تاريخ الدفع: {self.payment_date_edit.date().toString('yyyy-MM-dd')}\n"
            confirmation_message += f"تاريخ المبلغ المتبقي: {self.due_date_edit.date().toString('yyyy-MM-dd')}\n"

            if show_confirmation_message("تأكيد الإضافة", confirmation_message):
                # إنشاء فاتورة جديدة
                new_expense = Expense(
                    title=expense_number,
                    amount=required_amount,
                    date=self.payment_date_edit.date().toPyDate(),
                    category=status,
                    supplier_id=self.supplier.id,
                    notes=f"المبلغ المدفوع:{paid_amount}|تاريخ المبلغ المتبقي:{self.due_date_edit.date().toPyDate().strftime('%Y-%m-%d')}"
                )

                # إضافة الفاتورة إلى قاعدة البيانات
                self.session.add(new_expense)
                self.session.commit()

                # مسح حقول الإدخال
                self.required_amount_edit.clear()
                self.paid_amount_edit.clear()

                # تحديث البيانات المعروضة
                self.load_data()

                show_info_message("تم", "تمت إضافة البيانات المالية بنجاح")
            else:
                # إلغاء الإضافة
                return

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إضافة البيانات المالية: {str(e)}")

    def edit_financial_data(self, expense_id):
        """تعديل البيانات المالية"""
        try:
            # البحث عن المصروف في قاعدة البيانات
            expense = self.session.query(Expense).get(expense_id)
            if not expense:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # استخراج البيانات الحالية
            required_amount = expense.amount
            paid_amount = 0
            due_date = None

            # استخراج المبلغ المدفوع وتاريخ المبلغ المتبقي من الملاحظات
            if expense.notes:
                try:
                    if "المبلغ المدفوع:" in expense.notes:
                        paid_text = expense.notes.split("المبلغ المدفوع:")[1].split("|")[0].strip()
                        paid_amount = float(paid_text)

                    if "|تاريخ المبلغ المتبقي:" in expense.notes:
                        due_date_str = expense.notes.split("|تاريخ المبلغ المتبقي:")[1].strip()
                        due_date = QDate.fromString(due_date_str, "yyyy-MM-dd")
                except Exception as e:
                    print(f"خطأ في استخراج البيانات: {str(e)}")

            # تعيين القيم الحالية في حقول الإدخال
            self.required_amount_edit.setText(str(required_amount))
            self.paid_amount_edit.setText(str(paid_amount))

            # تعيين التواريخ
            if expense.date:
                self.payment_date_edit.setDate(QDate(expense.date))

            if due_date:
                self.due_date_edit.setDate(due_date)

            # تخزين معرف المصروف للاستخدام لاحقاً
            self.current_expense_id = expense_id

            # تغيير نص زر الإضافة إلى "تحديث"
            self.add_button.setText("تحديث")
            self.add_button.clicked.disconnect()
            self.add_button.clicked.connect(self.update_financial_data)

            # التمرير إلى أعلى النافذة لعرض حقول الإدخال
            self.scroll_to_top()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات للتعديل: {str(e)}")

    def update_financial_data(self):
        """تحديث البيانات المالية"""
        try:
            # التحقق من صحة البيانات المدخلة
            required_amount_text = self.required_amount_edit.text().strip()
            paid_amount_text = self.paid_amount_edit.text().strip()

            if not required_amount_text:
                show_error_message("خطأ", "يجب إدخال المبلغ المطلوب")
                return

            try:
                required_amount = float(required_amount_text)
                paid_amount = float(paid_amount_text) if paid_amount_text else 0.0
            except ValueError:
                show_error_message("خطأ", "يجب إدخال قيم رقمية صحيحة")
                return

            if required_amount <= 0:
                show_error_message("خطأ", "يجب أن يكون المبلغ المطلوب أكبر من صفر")
                return

            if paid_amount < 0:
                show_error_message("خطأ", "لا يمكن أن يكون المبلغ المدفوع سالباً")
                return

            # تحديد حالة المصروف
            if paid_amount == 0:
                status = 'pending'  # قيد الانتظار
            elif paid_amount >= required_amount:
                status = 'paid'  # مدفوع
                paid_amount = required_amount  # تصحيح المبلغ المدفوع إذا كان أكبر من المطلوب
            else:
                status = 'partially_paid'  # مدفوع جزئياً

            # البحث عن المصروف في قاعدة البيانات
            expense = self.session.query(Expense).get(self.current_expense_id)
            if not expense:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # تحديث بيانات المصروف
            expense.amount = required_amount
            expense.date = self.payment_date_edit.date().toPyDate()
            expense.category = status
            expense.notes = f"المبلغ المدفوع:{paid_amount}|تاريخ المبلغ المتبقي:{self.due_date_edit.date().toPyDate().strftime('%Y-%m-%d')}"

            # حفظ التغييرات
            self.session.commit()

            # إعادة تعيين زر الإضافة
            self.add_button.setText("إضافة")
            self.add_button.clicked.disconnect()
            self.add_button.clicked.connect(self.add_financial_data)

            # مسح حقول الإدخال
            self.required_amount_edit.clear()
            self.paid_amount_edit.clear()

            # تحديث البيانات المعروضة
            self.load_data()

            show_info_message("تم", "تم تحديث البيانات المالية بنجاح")

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث البيانات المالية: {str(e)}")

    def delete_financial_data(self, expense_id):
        """حذف البيانات المالية"""
        try:
            # البحث عن المصروف في قاعدة البيانات
            expense = self.session.query(Expense).get(expense_id)
            if not expense:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # طلب تأكيد الحذف
            if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذه الفاتورة؟"):
                # حذف المصروف
                self.session.delete(expense)
                self.session.commit()

                # تحديث البيانات المعروضة
                self.load_data()

                show_info_message("تم", "تم حذف الفاتورة بنجاح")

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}")

    def scroll_to_top(self):
        """التمرير إلى أعلى النافذة"""
        # يمكن تنفيذ هذه الدالة إذا كانت النافذة تحتوي على عنصر تمرير
        pass

    def save_changes(self):
        """حفظ التغييرات"""
        try:
            self.session.commit()
            show_info_message("تم", "تم حفظ التغييرات بنجاح")
        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ التغييرات: {str(e)}")

    def apply_filter(self):
        """تطبيق فلترة البيانات"""
        try:
            # الحصول على قيم الفلترة
            from_date = self.from_date_edit.date().toPyDate()
            to_date = self.to_date_edit.date().toPyDate()
            status = self.status_combo.currentData()

            # الحصول على مصروفات المورد مع تطبيق الفلترة
            query = self.session.query(Expense).filter_by(supplier_id=self.supplier.id)

            # فلترة حسب التاريخ
            query = query.filter(Expense.date >= from_date, Expense.date <= to_date)

            # فلترة حسب الحالة
            if status != "all":
                query = query.filter(Expense.category == status)

            # تنفيذ الاستعلام
            expenses = query.all()

            # تحديث الجدول
            self.update_table(expenses)

            # عرض رسالة تأكيد
            show_info_message("تم", "تم تطبيق الفلترة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تطبيق الفلترة: {str(e)}")

    def reset_filter(self):
        """إعادة تعيين الفلترة"""
        try:
            # إعادة تعيين حقول الفلترة
            self.from_date_edit.setDate(QDate.currentDate().addMonths(-1))
            self.to_date_edit.setDate(QDate.currentDate())
            self.status_combo.setCurrentIndex(0)  # "الكل"

            # إعادة تحميل البيانات
            self.load_data()

            # عرض رسالة تأكيد
            show_info_message("تم", "تم إعادة تعيين الفلترة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إعادة تعيين الفلترة: {str(e)}")

    def update_table(self, expenses):
        """تحديث جدول المصروفات بناءً على المصروفات المفلترة"""
        try:
            # حساب المبالغ
            total_due = sum(expense.amount for expense in expenses if expense.category != 'paid' and expense.category != 'cancelled')

            # استخراج المبلغ المدفوع من الملاحظات
            total_paid = 0
            for expense in expenses:
                if expense.notes and "المبلغ المدفوع:" in expense.notes:
                    try:
                        # استخراج المبلغ المدفوع من النص
                        paid_text = expense.notes.split("المبلغ المدفوع:")[1].split("|")[0].strip()
                        print(f"المبلغ المدفوع المستخرج في update_table: '{paid_text}'")
                        total_paid += float(paid_text)
                    except (ValueError, IndexError) as e:
                        print(f"خطأ في استخراج المبلغ المدفوع في update_table: {str(e)}")
                        print(f"النص الأصلي: '{expense.notes}'")
                        pass

            remaining = total_due - total_paid

            # عرض المبالغ مع تنسيق العملة
            self.total_due_label.setText(format_currency(total_due))
            self.total_paid_label.setText(format_currency(total_paid))
            self.remaining_label.setText(format_currency(remaining))

            # ملء جدول المصروفات
            self.expenses_table.setRowCount(0)
            for row, expense in enumerate(expenses):
                self.expenses_table.insertRow(row)
                self.expenses_table.setItem(row, 0, QTableWidgetItem(expense.title))

                # التاريخ مع تنسيق مناسب للغة العربية
                date_str = format_date(expense.date) if expense.date else ""
                self.expenses_table.setItem(row, 1, QTableWidgetItem(date_str))

                # تاريخ المبلغ المتبقي مع تنسيق مناسب للغة العربية
                due_date_str = ""
                if expense.notes and "|تاريخ المبلغ المتبقي:" in expense.notes:
                    try:
                        # استخراج تاريخ المبلغ المتبقي باستخدام الفاصل الجديد
                        raw_date_str = expense.notes.split("|تاريخ المبلغ المتبقي:")[1].strip()
                        # طباعة للتشخيص
                        print(f"تاريخ المبلغ المتبقي المستخرج: '{raw_date_str}'")
                        # تنسيق التاريخ بشكل مناسب للغة العربية
                        due_date_str = format_date(raw_date_str)
                    except (ValueError, IndexError) as e:
                        print(f"خطأ في استخراج تاريخ المبلغ المتبقي: {str(e)}")
                        print(f"النص الأصلي: '{expense.notes}'")
                self.expenses_table.setItem(row, 2, QTableWidgetItem(due_date_str))

                # المبلغ الإجمالي مع تنسيق العملة
                self.expenses_table.setItem(row, 3, QTableWidgetItem(format_currency(expense.amount)))

                # المبلغ المدفوع مع تنسيق العملة
                paid_amount = 0
                if expense.notes and "المبلغ المدفوع:" in expense.notes:
                    try:
                        # استخراج المبلغ المدفوع باستخدام الفاصل الجديد
                        paid_text = expense.notes.split("المبلغ المدفوع:")[1].split("|")[0].strip()
                        # طباعة للتشخيص
                        print(f"المبلغ المدفوع المستخرج: '{paid_text}'")
                        paid_amount = float(paid_text)
                    except (ValueError, IndexError) as e:
                        print(f"خطأ في استخراج المبلغ المدفوع: {str(e)}")
                        print(f"النص الأصلي: '{expense.notes}'")
                self.expenses_table.setItem(row, 4, QTableWidgetItem(format_currency(paid_amount)))

                # الحالة مع تمييز واضح باستخدام الألوان
                status_map = {
                    'pending': 'قيد الانتظار',
                    'paid': 'مدفوع',
                    'partially_paid': 'مدفوع جزئياً',
                    'cancelled': 'ملغى'
                }
                status_text = status_map.get(expense.category, expense.category)
                status_item = QTableWidgetItem(status_text)

                # تمييز الحالة باستخدام الألوان
                if expense.category == 'pending':
                    status_item.setBackground(Qt.yellow)  # أصفر لقيد الانتظار
                elif expense.category == 'paid':
                    status_item.setBackground(Qt.green)   # أخضر للمدفوع
                elif expense.category == 'partially_paid':
                    status_item.setBackground(Qt.cyan)    # أزرق فاتح للمدفوع جزئياً
                elif expense.category == 'cancelled':
                    status_item.setBackground(Qt.gray)    # رمادي للملغى

                self.expenses_table.setItem(row, 5, status_item)

                # إضافة زر التعديل
                edit_button = QPushButton("تعديل")
                edit_button
                edit_button.clicked.connect(lambda _, exp_id=expense.id: self.edit_financial_data(exp_id))
                self.expenses_table.setCellWidget(row, 6, edit_button)

                # إضافة زر الحذف
                delete_button = QPushButton("حذف")
                delete_button
                delete_button.clicked.connect(lambda _, exp_id=expense.id: self.delete_financial_data(exp_id))
                self.expenses_table.setCellWidget(row, 7, delete_button)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث الجدول: {str(e)}")

    def load_data(self):
        """تحميل البيانات المالية للمورد"""
        try:
            # الحصول على مصروفات المورد
            expenses = self.session.query(Expense).filter_by(supplier_id=self.supplier.id).all()

            # تحديث الجدول
            self.update_table(expenses)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات المالية: {str(e)}")

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إضافة العنوان الرئيسي مع الوصف في نفس السطر
        title_label = QLabel("🏪 إدارة الموردين - إدارة شاملة لبيانات الموردين مع أدوات متقدمة للبحث والتحليل")
        StyledLabel(title_label, 'title')
        main_layout.addWidget(title_label)

        # إنشاء شريط البحث والتصفية
        search_layout = QHBoxLayout()

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("بحث عن مورد...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet(UnifiedStyles.get_input_style())

        search_button = StyledButton("🔍 بحث", 'primary', 'normal')
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setMinimumWidth(80)

        # أزرار التصفية السريعة
        filter_all_button = StyledButton("الكل", 'secondary', 'normal')
        filter_all_button.clicked.connect(lambda: self.filter_by_balance('all'))
        filter_all_button.setMinimumWidth(60)

        filter_positive_button = StyledButton("لهم مبلغ", 'success', 'normal')
        filter_positive_button.clicked.connect(lambda: self.filter_by_balance('positive'))
        filter_positive_button.setMinimumWidth(80)

        filter_negative_button = StyledButton("عليهم مبلغ", 'danger', 'normal')
        filter_negative_button.clicked.connect(lambda: self.filter_by_balance('negative'))
        filter_negative_button.setMinimumWidth(80)

        filter_zero_button = StyledButton("بدون رصيد", 'warning', 'normal')
        filter_zero_button.clicked.connect(lambda: self.filter_by_balance('zero'))
        filter_zero_button.setMinimumWidth(80)

        search_label = StyledLabel("بحث:", 'subtitle')
        search_layout.addWidget(search_label.label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(search_button.button)
        filter_label = StyledLabel("تصفية:", 'subtitle')
        search_layout.addWidget(filter_label.label)
        search_layout.addWidget(filter_all_button.button)
        search_layout.addWidget(filter_positive_button.button)
        search_layout.addWidget(filter_negative_button.button)
        search_layout.addWidget(filter_zero_button.button)

        # إنشاء جدول الموردين مع العرض الكامل
        styled_table = StyledTable()
        self.suppliers_table = styled_table.table
        self.suppliers_table.setColumnCount(7)
        self.suppliers_table.setHorizontalHeaderLabels(["الرقم", "الاسم", "رقم الهاتف", "البريد الإلكتروني", "العنوان", "الرصيد (ر.س)", "ملاحظات"])
        # تمكين التلميحات عند تمرير المؤشر
        self.suppliers_table.setMouseTracking(True)
        self.suppliers_table.cellEntered.connect(self.show_cell_tooltip)
        # النمط والتوسيع مطبق بالفعل من خلال StyledTable

        # إنشاء أزرار الإجراءات مرتبة حسب الأولوية
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)  # مسافة مناسبة بين الأزرار

        # العمليات الأساسية مرتبة: إضافة، تعديل، حذف، عرض التفاصيل، تعديل الرصيد، تصدير، تحديث
        self.add_button = StyledButton("➕ إضافة مورد", 'success', 'normal')
        self.add_button.clicked.connect(self.add_supplier)
        self.add_button.setMinimumWidth(120)

        self.edit_button = StyledButton("✏️ تعديل", 'primary', 'normal')
        self.edit_button.clicked.connect(self.edit_supplier)
        self.edit_button.setMinimumWidth(100)

        self.delete_button = StyledButton("🗑️ حذف", 'danger', 'normal')
        self.delete_button.clicked.connect(self.delete_supplier)
        self.delete_button.setMinimumWidth(100)

        # زر عرض التفاصيل مع قائمة منسدلة متطورة
        self.details_button = StyledButton("👁️ عرض التفاصيل", 'info', 'normal')
        self.details_button.setMinimumWidth(120)

        # إنشاء قائمة منسدلة لعرض التفاصيل
        details_menu = QMenu(self)
        details_menu.setStyleSheet(UnifiedStyles.get_menu_style())

        # إضافة خيارات عرض التفاصيل
        basic_details_action = QAction("👤 التفاصيل الأساسية", self)
        basic_details_action.triggered.connect(self.show_supplier_details)
        details_menu.addAction(basic_details_action)

        financial_details_action = QAction("💰 التفاصيل المالية", self)
        financial_details_action.triggered.connect(self.show_financial_details)
        details_menu.addAction(financial_details_action)

        contact_details_action = QAction("📞 تفاصيل الاتصال", self)
        contact_details_action.triggered.connect(self.show_contact_details)
        details_menu.addAction(contact_details_action)

        history_details_action = QAction("📋 سجل المعاملات", self)
        history_details_action.triggered.connect(self.show_transaction_history)
        details_menu.addAction(history_details_action)

        # ربط القائمة بالزر
        self.details_button.setMenu(details_menu)

        self.balance_button = StyledButton("💰 تعديل الرصيد", 'warning', 'normal')
        self.balance_button.clicked.connect(self.adjust_balance)
        self.balance_button.setMinimumWidth(120)

        # زر التصدير مع قائمة منسدلة متطورة
        self.export_button = StyledButton("📤 تصدير", 'secondary', 'normal')
        self.export_button.setMinimumWidth(100)

        # إنشاء قائمة منسدلة للتصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style())

        # إضافة خيارات التصدير
        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_data)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        # ربط القائمة بالزر
        self.export_button.setMenu(export_menu)

        # زر الإحصائيات مع قائمة منسدلة متطورة
        self.statistics_button = StyledButton("📊 إحصائيات", 'info', 'normal')
        self.statistics_button.setMinimumWidth(100)

        # إنشاء قائمة منسدلة للإحصائيات
        statistics_menu = QMenu(self)
        statistics_menu.setStyleSheet(UnifiedStyles.get_menu_style())

        # إضافة خيارات الإحصائيات
        basic_stats_action = QAction("📈 إحصائيات أساسية", self)
        basic_stats_action.triggered.connect(self.show_statistics)
        statistics_menu.addAction(basic_stats_action)

        detailed_stats_action = QAction("📊 إحصائيات مفصلة", self)
        detailed_stats_action.triggered.connect(self.show_detailed_statistics)
        statistics_menu.addAction(detailed_stats_action)

        balance_analysis_action = QAction("💰 تحليل الأرصدة", self)
        balance_analysis_action.triggered.connect(self.show_balance_analysis)
        statistics_menu.addAction(balance_analysis_action)

        monthly_report_action = QAction("📅 تقرير شهري", self)
        monthly_report_action.triggered.connect(self.show_monthly_report)
        statistics_menu.addAction(monthly_report_action)

        # ربط القائمة بالزر
        self.statistics_button.setMenu(statistics_menu)

        self.backup_button = StyledButton("💾 نسخ احتياطي", 'warning', 'normal')
        self.backup_button.clicked.connect(self.backup_suppliers_data)
        self.backup_button.setMinimumWidth(120)

        self.restore_button = StyledButton("📥 استعادة", 'info', 'normal')
        self.restore_button.clicked.connect(self.restore_suppliers_data)
        self.restore_button.setMinimumWidth(100)

        self.report_button = StyledButton("📋 تقرير", 'primary', 'normal')
        self.report_button.clicked.connect(self.generate_suppliers_report)
        self.report_button.setMinimumWidth(100)

        self.refresh_button = StyledButton("🔄 تحديث", 'secondary', 'normal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setMinimumWidth(100)

        # إضافة الأزرار بالترتيب المطلوب
        actions_layout.addWidget(self.add_button.button)
        actions_layout.addWidget(self.edit_button.button)
        actions_layout.addWidget(self.delete_button.button)
        actions_layout.addWidget(self.balance_button.button)
        actions_layout.addWidget(self.details_button.button)
        actions_layout.addWidget(self.export_button.button)
        actions_layout.addWidget(self.statistics_button.button)
        actions_layout.addWidget(self.report_button.button)
        actions_layout.addWidget(self.backup_button.button)
        actions_layout.addWidget(self.restore_button.button)
        actions_layout.addWidget(self.refresh_button.button)

        # تجميع التخطيط النهائي
        main_layout.addLayout(search_layout)
        main_layout.addWidget(self.suppliers_table)
        main_layout.addLayout(actions_layout)

        self.setLayout(main_layout)

    def refresh_data(self):
        """تحديث بيانات الموردين في الجدول"""
        # الحصول على جميع الموردين من قاعدة البيانات
        suppliers = self.session.query(Supplier).all()
        self.populate_table(suppliers)

    def populate_table(self, suppliers):
        """ملء جدول الموردين بالبيانات"""
        self.suppliers_table.setRowCount(0)

        for row, supplier in enumerate(suppliers):
            self.suppliers_table.insertRow(row)
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier.id)))
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.name))
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.phone or ""))
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.email or ""))
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.address or ""))

            # إضافة الرصيد مع تنسيق العملة
            balance_item = QTableWidgetItem(f"{supplier.balance:.2f}")
            # تلوين الرصيد حسب القيمة (أحمر للسالب، أخضر للموجب)
            if supplier.balance < 0:
                balance_item.setForeground(QColor(255, 0, 0))  # أحمر غامق
                balance_item.setBackground(QColor(255, 235, 235))  # خلفية حمراء فاتحة
                balance_item.setToolTip(f"مبلغ مستحق على المورد: {abs(supplier.balance):.2f} ر.س")
            elif supplier.balance > 0:
                balance_item.setForeground(QColor(0, 128, 0))  # أخضر غامق
                balance_item.setBackground(QColor(235, 255, 235))  # خلفية خضراء فاتحة
                balance_item.setToolTip(f"مبلغ مستحق للمورد: {supplier.balance:.2f} ر.س")
            else:
                balance_item.setToolTip("لا يوجد رصيد مستحق")
            self.suppliers_table.setItem(row, 5, balance_item)

            # إضافة الملاحظات
            self.suppliers_table.setItem(row, 6, QTableWidgetItem(supplier.notes or ""))

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            self.refresh_data()
            return

        # البحث في قاعدة البيانات
        suppliers = self.session.query(Supplier).filter(
            Supplier.name.like(f"%{search_text}%") |
            Supplier.phone.like(f"%{search_text}%") |
            Supplier.email.like(f"%{search_text}%") |
            Supplier.address.like(f"%{search_text}%")
        ).all()

        self.populate_table(suppliers)

    def filter_by_balance(self, balance_type='all'):
        """تصفية الموردين حسب نوع الرصيد"""
        if balance_type == 'positive':
            # الموردين الذين لهم مبلغ
            suppliers = self.session.query(Supplier).filter(Supplier.balance > 0).all()
        elif balance_type == 'negative':
            # الموردين الذين عليهم مبلغ
            suppliers = self.session.query(Supplier).filter(Supplier.balance < 0).all()
        elif balance_type == 'zero':
            # الموردين بدون رصيد
            suppliers = self.session.query(Supplier).filter(Supplier.balance == 0).all()
        else:
            # جميع الموردين
            suppliers = self.session.query(Supplier).all()

        self.populate_table(suppliers)

    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # استخراج أرقام الهواتف من البيانات
                phones = data.pop('phones', [])

                # إنشاء مورد جديد في قاعدة البيانات
                supplier = Supplier(**data)
                self.session.add(supplier)
                self.session.flush()  # للحصول على معرف المورد

                # إضافة أرقام الهواتف
                for phone in phones:
                    phone.supplier_id = supplier.id
                    self.session.add(phone)

                self.session.commit()

                show_info_message("تم", "تمت إضافة المورد بنجاح")
                self.refresh_data()

    def edit_supplier(self):
        """تعديل بيانات مورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        dialog = SupplierDialog(self, supplier, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # استخراج أرقام الهواتف من البيانات
                phones = data.pop('phones', [])

                # تحديث بيانات المورد
                for key, value in data.items():
                    setattr(supplier, key, value)

                # حذف جميع أرقام الهواتف الحالية
                self.session.query(SupplierPhone).filter_by(supplier_id=supplier.id).delete()

                # إضافة أرقام الهواتف الجديدة
                for phone in phones:
                    phone.supplier_id = supplier.id
                    self.session.add(phone)

                self.session.commit()
                show_info_message("تم", "تم تحديث بيانات المورد بنجاح")
                self.refresh_data()

    def delete_supplier(self):
        """حذف مورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        # التأكد من أن المورد ليس لديه فواتير مرتبطة
        if supplier.expenses:
            show_error_message(
                "خطأ",
                f"لا يمكن حذف المورد لأنه مرتبط بـ {len(supplier.expenses)} فاتورة. قم بحذف الفواتير أولاً."
            )
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف المورد {supplier.name}؟"):
            try:
                # حذف أرقام الهواتف المرتبطة بالمورد
                from database import SupplierPhone
                self.session.query(SupplierPhone).filter_by(supplier_id=supplier.id).delete()

                # حذف المورد
                self.session.delete(supplier)
                self.session.commit()
                show_info_message("تم", "تم حذف المورد بنجاح")
                self.refresh_data()
            except Exception as e:
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء حذف المورد: {str(e)}")

    def adjust_balance(self):
        """تعديل المبلغ المستحق للمورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        dialog = BalanceAdjustmentDialog(self, supplier)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث المبلغ المستحق
                success, new_balance = update_supplier_balance(
                    self.session,
                    supplier_id,
                    data['amount'],
                    data['operation']
                )

                if success:
                    show_info_message(
                        "تم",
                        f"تم تحديث المبلغ المستحق للمورد {supplier.name} بنجاح.\nالمبلغ الجديد: {new_balance:.2f}"
                    )
                    self.refresh_data()
                else:
                    show_error_message("خطأ", "حدث خطأ أثناء تحديث المبلغ المستحق")

    def show_financial_details(self):
        """عرض التفاصيل المالية للمورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        try:
            dialog = SupplierFinancialDetailsDialog(self, supplier, self.session)
            dialog.exec_()
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التفاصيل المالية: {str(e)}")
            import traceback
            print(f"خطأ في عرض التفاصيل المالية: {str(e)}")
            print(traceback.format_exc())

    def show_cell_tooltip(self, row, column):
        """عرض تلميح عند تمرير المؤشر فوق خلية"""
        item = self.suppliers_table.item(row, column)

        if column == 2:  # عمود رقم الهاتف
            if item and item.text():
                # الحصول على معرف المورد
                supplier_id = int(self.suppliers_table.item(row, 0).text())

                # البحث عن أرقام هواتف المورد
                from database import SupplierPhone
                phone_records = self.session.query(SupplierPhone).filter_by(supplier_id=supplier_id).all()

                if phone_records:
                    # إنشاء نص التلميح مع جميع أرقام الهواتف
                    tooltip_text = "أرقام الهواتف:\n"
                    for phone in phone_records:
                        phone_label = f" ({phone.label})" if phone.label else ""
                        primary_mark = " [رئيسي]" if phone.is_primary else ""
                        tooltip_text += f"• {phone.phone_number}{phone_label}{primary_mark}\n"

                    self.suppliers_table.setToolTip(tooltip_text)
                else:
                    self.suppliers_table.setToolTip(f"رقم الهاتف: {item.text()}")
            else:
                self.suppliers_table.setToolTip("")
        elif item and column == 6:  # عمود الملاحظات
            if item.text():
                # تعيين تلميح للملاحظات الطويلة
                self.suppliers_table.setToolTip(item.text())
            else:
                self.suppliers_table.setToolTip("")
        else:
            # استخدام التلميح المعين مسبقاً للخلية إن وجد
            if item and item.toolTip():
                self.suppliers_table.setToolTip(item.toolTip())
            else:
                self.suppliers_table.setToolTip("")

    def export_data(self):
        """تصدير بيانات الموردين إلى ملف Excel"""
        try:
            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف Excel", "قائمة_الموردين.xlsx", "ملفات Excel (*.xlsx)")
            if not file_path:
                return

            try:
                # استخدام مكتبة pandas لتصدير البيانات
                import pandas as pd

                # جمع البيانات من الجدول
                data = []
                headers = []

                # الحصول على عناوين الأعمدة
                for col in range(self.suppliers_table.columnCount()):
                    headers.append(self.suppliers_table.horizontalHeaderItem(col).text())

                # جمع البيانات من الجدول
                for row in range(self.suppliers_table.rowCount()):
                    row_data = []
                    for col in range(self.suppliers_table.columnCount()):
                        item = self.suppliers_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

                # إنشاء DataFrame
                df = pd.DataFrame(data, columns=headers)

                # حفظ البيانات إلى ملف Excel
                df.to_excel(file_path, index=False)

                show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
            except ImportError:
                # في حالة عدم وجود مكتبة pandas
                show_error_message("خطأ", "يرجى تثبيت مكتبة pandas لتصدير البيانات:\npip install pandas openpyxl")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_pdf(self):
        """تصدير بيانات الموردين إلى ملف PDF"""
        show_info_message("قريباً", "ميزة التصدير إلى PDF ستكون متاحة قريباً")

    def export_to_csv(self):
        """تصدير بيانات الموردين إلى ملف CSV"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_الموردين.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.suppliers_table.columnCount()):
                headers.append(self.suppliers_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.suppliers_table.rowCount()):
                row_data = []
                for col in range(self.suppliers_table.columnCount()):
                    item = self.suppliers_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_json(self):
        """تصدير بيانات الموردين إلى ملف JSON"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import json

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_الموردين.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.suppliers_table.columnCount()):
                headers.append(self.suppliers_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.suppliers_table.rowCount()):
                row_data = {}
                for col in range(self.suppliers_table.columnCount()):
                    item = self.suppliers_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def show_contact_details(self):
        """عرض تفاصيل الاتصال للمورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        try:
            # البحث عن أرقام هواتف المورد
            phone_records = self.session.query(SupplierPhone).filter_by(supplier_id=supplier.id).all()

            # إنشاء نص تفاصيل الاتصال مختصر
            primary_phone = supplier.phone or 'غير متوفر'
            if phone_records:
                primary_phone = phone_records[0].phone_number

            contact_info = f"📞 {supplier.name} | 📱 {primary_phone} | 📧 {supplier.email or 'غير متوفر'} | 📍 {supplier.address or 'غير متوفر'}"

            show_info_message("تفاصيل الاتصال", contact_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض تفاصيل الاتصال: {str(e)}")

    def show_transaction_history(self):
        """عرض سجل المعاملات للمورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        try:
            # البحث عن مصروفات المورد
            expenses = self.session.query(Expense).filter_by(supplier_id=supplier.id).order_by(Expense.date.desc()).all()

            # إنشاء نص سجل المعاملات مختصر
            total_amount = sum(expense.amount for expense in expenses) if expenses else 0
            history_info = f"📋 {supplier.name} | 💰 الرصيد: {supplier.balance:.2f} ر.س | 📊 المعاملات: {len(expenses)} | 💵 الإجمالي: {total_amount:.2f} ر.س"

            show_info_message("سجل المعاملات", history_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض سجل المعاملات: {str(e)}")

    def get_supplier_statistics(self):
        """الحصول على إحصائيات الموردين"""
        try:
            # إجمالي عدد الموردين
            total_suppliers = self.session.query(Supplier).count()

            # الموردين الذين لديهم رصيد موجب (لهم مبلغ)
            suppliers_with_positive_balance = self.session.query(Supplier).filter(Supplier.balance > 0).count()

            # الموردين الذين لديهم رصيد سالب (عليهم مبلغ)
            suppliers_with_negative_balance = self.session.query(Supplier).filter(Supplier.balance < 0).count()

            # إجمالي المبالغ المستحقة للموردين
            total_positive_balance = self.session.query(Supplier).filter(Supplier.balance > 0).with_entities(
                self.session.query(Supplier.balance).filter(Supplier.balance > 0).subquery().c.balance
            ).all()
            total_positive = sum([balance[0] for balance in total_positive_balance]) if total_positive_balance else 0

            # إجمالي المبالغ المستحقة على الموردين
            total_negative_balance = self.session.query(Supplier).filter(Supplier.balance < 0).with_entities(
                self.session.query(Supplier.balance).filter(Supplier.balance < 0).subquery().c.balance
            ).all()
            total_negative = sum([abs(balance[0]) for balance in total_negative_balance]) if total_negative_balance else 0

            return {
                'total_suppliers': total_suppliers,
                'suppliers_with_positive_balance': suppliers_with_positive_balance,
                'suppliers_with_negative_balance': suppliers_with_negative_balance,
                'total_positive_balance': total_positive,
                'total_negative_balance': total_negative
            }
        except Exception as e:
            print(f"خطأ في حساب إحصائيات الموردين: {str(e)}")
            return None

    def show_statistics(self):
        """عرض الإحصائيات الأساسية للموردين"""
        stats = self.get_supplier_statistics()
        if not stats:
            show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات")
            return

        stats_text = f"""📊 إحصائيات الموردين:

📈 الإحصائيات العامة:
• إجمالي عدد الموردين: {stats['total_suppliers']}
• موردين لهم مبلغ: {stats['suppliers_with_positive_balance']}
• موردين عليهم مبلغ: {stats['suppliers_with_negative_balance']}
• موردين بدون رصيد: {stats['total_suppliers'] - stats['suppliers_with_positive_balance'] - stats['suppliers_with_negative_balance']}

💰 الإحصائيات المالية:
• إجمالي المبالغ المستحقة للموردين: {format_currency(stats['total_positive_balance'])}
• إجمالي المبالغ المستحقة على الموردين: {format_currency(stats['total_negative_balance'])}
• صافي المبلغ: {format_currency(stats['total_positive_balance'] - stats['total_negative_balance'])}"""

        show_info_message("إحصائيات الموردين", stats_text)

    def show_detailed_statistics(self):
        """عرض الإحصائيات المفصلة للموردين"""
        stats = self.get_supplier_statistics()
        if not stats:
            show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات")
            return

        # حساب إحصائيات إضافية
        try:
            # الموردين الأكثر نشاطاً (حسب عدد المصروفات)
            from sqlalchemy import func
            top_suppliers = self.session.query(
                Supplier.name,
                func.count(Expense.id).label('expense_count'),
                func.sum(Expense.amount).label('total_amount')
            ).join(Expense).group_by(Supplier.id).order_by(func.count(Expense.id).desc()).limit(5).all()

            detailed_stats = f"""📊 إحصائيات مفصلة للموردين:

📈 الإحصائيات العامة:
• إجمالي عدد الموردين: {stats['total_suppliers']}
• موردين نشطين (لديهم مصروفات): {len(top_suppliers)}
• متوسط الرصيد للمورد: {format_currency((stats['total_positive_balance'] - stats['total_negative_balance']) / max(stats['total_suppliers'], 1))}

💰 التحليل المالي:
• إجمالي المبالغ المستحقة للموردين: {format_currency(stats['total_positive_balance'])}
• إجمالي المبالغ المستحقة على الموردين: {format_currency(stats['total_negative_balance'])}
• صافي المبلغ: {format_currency(stats['total_positive_balance'] - stats['total_negative_balance'])}

🏆 أكثر الموردين نشاطاً:"""

            for i, supplier in enumerate(top_suppliers[:3], 1):
                detailed_stats += f"""
{i}. {supplier.name}
    • عدد المصروفات: {supplier.expense_count}
    • إجمالي المبلغ: {format_currency(supplier.total_amount or 0)}"""

            show_info_message("إحصائيات مفصلة", detailed_stats)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حساب الإحصائيات المفصلة: {str(e)}")

    def show_balance_analysis(self):
        """عرض تحليل الأرصدة"""
        stats = self.get_supplier_statistics()
        if not stats:
            show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات")
            return

        # حساب النسب المئوية
        total = stats['total_suppliers']
        positive_percent = (stats['suppliers_with_positive_balance'] / max(total, 1)) * 100
        negative_percent = (stats['suppliers_with_negative_balance'] / max(total, 1)) * 100
        zero_percent = 100 - positive_percent - negative_percent

        analysis_text = f"""💰 تحليل أرصدة الموردين:

📊 توزيع الأرصدة:
• موردين لهم مبلغ: {stats['suppliers_with_positive_balance']} ({positive_percent:.1f}%)
• موردين عليهم مبلغ: {stats['suppliers_with_negative_balance']} ({negative_percent:.1f}%)
• موردين بدون رصيد: {total - stats['suppliers_with_positive_balance'] - stats['suppliers_with_negative_balance']} ({zero_percent:.1f}%)

💵 التحليل المالي:
• إجمالي المستحقات للموردين: {format_currency(stats['total_positive_balance'])}
• إجمالي المستحقات على الموردين: {format_currency(stats['total_negative_balance'])}
• الفرق (صافي المبلغ): {format_currency(stats['total_positive_balance'] - stats['total_negative_balance'])}

📈 التوصيات:
• {'تحصيل المستحقات من الموردين' if stats['total_negative_balance'] > stats['total_positive_balance'] else 'دفع المستحقات للموردين'}
• مراجعة الأرصدة بانتظام
• تسوية الحسابات المعلقة"""

        show_info_message("تحليل الأرصدة", analysis_text)

    def show_monthly_report(self):
        """عرض التقرير الشهري"""
        try:
            from datetime import datetime, timedelta

            # الحصول على بداية ونهاية الشهر الحالي
            now = datetime.now()
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                end_of_month = start_of_month.replace(year=now.year + 1, month=1) - timedelta(days=1)
            else:
                end_of_month = start_of_month.replace(month=now.month + 1) - timedelta(days=1)

            # الحصول على مصروفات الشهر الحالي
            monthly_expenses = self.session.query(Expense).filter(
                Expense.date >= start_of_month,
                Expense.date <= end_of_month
            ).all()

            total_monthly_amount = sum(expense.amount for expense in monthly_expenses)
            suppliers_count = len(set(expense.supplier_id for expense in monthly_expenses if expense.supplier_id))

            monthly_report = f"""📅 التقرير الشهري - {now.strftime('%B %Y')}:

📊 إحصائيات الشهر:
• عدد المصروفات: {len(monthly_expenses)}
• عدد الموردين النشطين: {suppliers_count}
• إجمالي المبالغ: {format_currency(total_monthly_amount)}
• متوسط المصروف: {format_currency(total_monthly_amount / max(len(monthly_expenses), 1))}

📈 مقارنة مع الشهر السابق:
• (سيتم إضافة هذه الميزة لاحقاً)

📝 ملاحظات:
• تم حساب البيانات من {start_of_month.strftime('%Y-%m-%d')} إلى {end_of_month.strftime('%Y-%m-%d')}
• البيانات تشمل جميع المصروفات المسجلة في النظام"""

            show_info_message("التقرير الشهري", monthly_report)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير الشهري: {str(e)}")

    def backup_suppliers_data(self):
        """إنشاء نسخة احتياطية من بيانات الموردين"""
        try:
            import json
            from datetime import datetime

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ النسخة الاحتياطية",
                f"نسخة_احتياطية_الموردين_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "ملفات JSON (*.json)"
            )
            if not file_path:
                return

            # جمع بيانات الموردين
            suppliers = self.session.query(Supplier).all()
            backup_data = {
                'backup_date': datetime.now().isoformat(),
                'suppliers_count': len(suppliers),
                'suppliers': []
            }

            for supplier in suppliers:
                supplier_data = {
                    'name': supplier.name,
                    'phone': supplier.phone,
                    'email': supplier.email,
                    'address': supplier.address,
                    'balance': supplier.balance,
                    'notes': supplier.notes
                }

                # إضافة أرقام الهواتف المتعددة
                phones = self.session.query(SupplierPhone).filter_by(supplier_id=supplier.id).all()
                supplier_data['phones'] = [
                    {
                        'phone_number': phone.phone_number,
                        'label': phone.label,
                        'is_primary': phone.is_primary
                    } for phone in phones
                ]

                backup_data['suppliers'].append(supplier_data)

            # حفظ البيانات
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_suppliers_data(self):
        """استعادة بيانات الموردين من نسخة احتياطية"""
        try:
            import json

            # عرض مربع حوار فتح الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار النسخة الاحتياطية",
                "",
                "ملفات JSON (*.json)"
            )
            if not file_path:
                return

            # تأكيد الاستعادة
            if not show_confirmation_message(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة البيانات؟\nسيتم استبدال البيانات الحالية."
            ):
                return

            # قراءة البيانات
            with open(file_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # التحقق من صحة البيانات
            if 'suppliers' not in backup_data:
                show_error_message("خطأ", "ملف النسخة الاحتياطية غير صحيح")
                return

            # استعادة البيانات
            restored_count = 0
            for supplier_data in backup_data['suppliers']:
                # التحقق من وجود المورد
                existing_supplier = self.session.query(Supplier).filter_by(name=supplier_data['name']).first()

                if not existing_supplier:
                    # إنشاء مورد جديد
                    supplier = Supplier(
                        name=supplier_data['name'],
                        phone=supplier_data.get('phone'),
                        email=supplier_data.get('email'),
                        address=supplier_data.get('address'),
                        balance=supplier_data.get('balance', 0),
                        notes=supplier_data.get('notes')
                    )
                    self.session.add(supplier)
                    self.session.flush()

                    # إضافة أرقام الهواتف
                    for phone_data in supplier_data.get('phones', []):
                        phone = SupplierPhone(
                            supplier_id=supplier.id,
                            phone_number=phone_data['phone_number'],
                            label=phone_data.get('label'),
                            is_primary=phone_data.get('is_primary', False)
                        )
                        self.session.add(phone)

                    restored_count += 1

            self.session.commit()
            show_info_message("تم", f"تم استعادة {restored_count} مورد بنجاح")
            self.refresh_data()

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء استعادة البيانات: {str(e)}")

    def generate_suppliers_report(self):
        """إنشاء تقرير شامل للموردين"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير الموردين",
                "تقرير_الموردين.pdf",
                "ملفات PDF (*.pdf)"
            )
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_suppliers_report_html()
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم إنشاء التقرير بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def generate_suppliers_report_html(self):
        """إنشاء محتوى HTML لتقرير الموردين"""
        try:
            import datetime

            # الحصول على جميع الموردين
            suppliers = self.session.query(Supplier).all()
            stats = self.get_supplier_statistics()

            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير الموردين</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #2c3e50; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #3498db; color: white; }}
                    .summary {{ background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .positive-balance {{ color: #27ae60; font-weight: bold; }}
                    .negative-balance {{ color: #e74c3c; font-weight: bold; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير الموردين</h1>
                <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

                <div class="summary">
                    <h2>📊 ملخص الإحصائيات</h2>
                    <p>إجمالي عدد الموردين: <strong>{stats['total_suppliers'] if stats else 0}</strong></p>
                    <p>موردين لهم مبلغ: <strong>{stats['suppliers_with_positive_balance'] if stats else 0}</strong></p>
                    <p>موردين عليهم مبلغ: <strong>{stats['suppliers_with_negative_balance'] if stats else 0}</strong></p>
                    <p>إجمالي المبالغ المستحقة للموردين: <strong>{format_currency(stats['total_positive_balance']) if stats else '0.00 ر.س'}</strong></p>
                    <p>إجمالي المبالغ المستحقة على الموردين: <strong>{format_currency(stats['total_negative_balance']) if stats else '0.00 ر.س'}</strong></p>
                </div>

                <h2>📋 قائمة الموردين</h2>
                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>العنوان</th>
                        <th>الرصيد (ر.س)</th>
                        <th>ملاحظات</th>
                    </tr>
            """

            # إضافة بيانات الموردين
            for supplier in suppliers:
                balance_class = ""
                if supplier.balance > 0:
                    balance_class = "positive-balance"
                elif supplier.balance < 0:
                    balance_class = "negative-balance"

                html_content += f"""
                    <tr>
                        <td>{supplier.id}</td>
                        <td>{supplier.name}</td>
                        <td>{supplier.phone or 'غير متوفر'}</td>
                        <td>{supplier.email or 'غير متوفر'}</td>
                        <td>{supplier.address or 'غير متوفر'}</td>
                        <td class="{balance_class}">{supplier.balance:.2f}</td>
                        <td>{supplier.notes or 'لا توجد ملاحظات'}</td>
                    </tr>
                """

            html_content += """
                </table>
            </body>
            </html>
            """

            return html_content
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير الموردين: {str(e)}</p>
            </body>
            </html>
            """

    def show_supplier_details(self):
        """عرض تفاصيل المورد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        try:
            dialog = SupplierDetailsDialog(self, supplier)
            dialog.exec_()
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل المورد: {str(e)}")
            import traceback
            print(f"خطأ في عرض تفاصيل المورد: {str(e)}")
            print(traceback.format_exc())

class SupplierDetailsDialog(BaseDialog):
    """نافذة حوار لعرض تفاصيل المورد"""

    def __init__(self, parent=None, supplier=None):
        title = f"👤 تفاصيل المورد: {supplier.name if supplier else 'غير محدد'}"
        super().__init__(parent, title, "xlarge")
        self.supplier = supplier
        self.session = parent.session if parent else None
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # عنوان النافذة مع النمط المتطور
        title_label = StyledLabel(f"👤 تفاصيل المورد: {self.supplier.name}", 'hero')
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # وصف توضيحي
        desc_label = StyledLabel("عرض شامل لمعلومات المورد وتفاصيل الاتصال", 'title')
        desc_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(desc_label)

        # إنشاء مجموعة البيانات الأساسية مع النمط المتطور
        details_group = StyledGroupBox("📋 المعلومات الأساسية")
        details_layout = QFormLayout()
        details_layout.setContentsMargins(20, 25, 20, 25)
        details_layout.setSpacing(20)
        details_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # عرض البيانات مع النمط المتطور
        name_title = StyledLabel("الاسم:", 'subtitle')
        name_value = StyledLabel(self.supplier.name, 'normal')
        details_layout.addRow(name_title, name_value)

        # عرض أرقام الهواتف المتعددة
        try:
            # البحث عن أرقام هواتف المورد
            from database import SupplierPhone
            phone_records = self.session.query(SupplierPhone).filter_by(supplier_id=self.supplier.id).all()
            print(f"تم العثور على {len(phone_records)} رقم هاتف للمورد {self.supplier.name}")

            if phone_records:
                # إنشاء قائمة لعرض أرقام الهواتف
                phones_list = QListWidget()
                phones_list

                # إضافة أرقام الهواتف إلى القائمة
                for phone in phone_records:
                    phone_label = f" ({phone.label})" if phone.label else ""
                    primary_mark = " [رئيسي]" if phone.is_primary else ""
                    label_text = f"{phone.phone_number}{phone_label}{primary_mark}"

                    item = QListWidgetItem(label_text)
                    item.setTextAlignment(Qt.AlignCenter)

                    # تمييز الرقم الرئيسي
                    if phone.is_primary:
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                        item.setForeground(QColor("#0078D7"))

                    phones_list.addItem(item)

                # إضافة القائمة إلى التخطيط مع النمط المتطور
                phones_title = StyledLabel("📱 أرقام الهواتف:", 'subtitle')
                details_layout.addRow(phones_title, phones_list)
            else:
                # إذا لم تكن هناك أرقام هواتف متعددة، عرض الرقم الرئيسي من جدول الموردين
                print(f"لا توجد أرقام هواتف متعددة، عرض الرقم الرئيسي: {self.supplier.phone}")

                # إنشاء قائمة لعرض الرقم الرئيسي بنفس التنسيق
                phones_list = QListWidget()
                phones_list.setStyleSheet(UnifiedStyles.get_input_style())

                if self.supplier.phone:
                    item = QListWidgetItem(f"{self.supplier.phone} [رئيسي]")
                    item.setTextAlignment(Qt.AlignCenter)
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                    item.setForeground(QColor("#0078D7"))
                    phones_list.addItem(item)
                else:
                    item = QListWidgetItem("لا يوجد رقم هاتف")
                    item.setTextAlignment(Qt.AlignCenter)
                    phones_list.addItem(item)

                phones_title = StyledLabel("📱 أرقام الهواتف:", 'subtitle')
                details_layout.addRow(phones_title, phones_list)
        except Exception as e:
            print(f"خطأ في عرض أرقام الهواتف: {str(e)}")
            import traceback
            traceback.print_exc()

            # في حالة الخطأ، عرض الرقم الرئيسي فقط
            phones_title = StyledLabel("📱 رقم الهاتف:", 'subtitle')
            phones_value = StyledLabel(self.supplier.phone or "غير متوفر", 'normal')
            details_layout.addRow(phones_title, phones_value)

        # البريد الإلكتروني
        email_title = StyledLabel("📧 البريد الإلكتروني:", 'subtitle')
        email_value = StyledLabel(self.supplier.email or "غير متوفر", 'normal')
        details_layout.addRow(email_title, email_value)

        # العنوان
        address_title = StyledLabel("📍 العنوان:", 'subtitle')
        address_value = StyledLabel(self.supplier.address or "غير متوفر", 'normal')
        details_layout.addRow(address_title, address_value)

        # إضافة المجموعات إلى التخطيط الرئيسي
        details_group.setLayout(details_layout)
        main_layout.addWidget(details_group)

        # إضافة الملاحظات مع النمط المتطور
        notes_group = StyledGroupBox("📝 ملاحظات إضافية")
        notes_layout = QVBoxLayout()
        notes_layout.setContentsMargins(20, 25, 20, 25)
        notes_layout.setSpacing(15)

        notes_text = QTextEdit()
        notes_text.setReadOnly(True)
        notes_text.setText(self.supplier.notes or "لا توجد ملاحظات إضافية لهذا المورد")
        notes_text.setStyleSheet(UnifiedStyles.get_input_style())
        notes_text.setMaximumHeight(120)

        notes_layout.addWidget(notes_text)
        notes_group.setLayout(notes_layout)

        # إضافة مجموعة الملاحظات إلى التخطيط الرئيسي
        main_layout.addWidget(notes_group)

        # أزرار الإغلاق مع النمط المتطور
        button_layout = self.create_button_layout([
            ('close', '❌ إغلاق النافذة', 'secondary', self.accept)
        ])

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

class SupplierFinancialDetailsDialog(BaseDialog):
    """نافذة حوار لعرض التفاصيل المالية للمورد"""

    def __init__(self, parent=None, supplier=None, session=None):
        title = f"💰 التفاصيل المالية: {supplier.name if supplier else 'غير محدد'}"
        super().__init__(parent, title, "xxlarge")
        self.supplier = supplier
        self.session = session
        self.init_ui()
        self.load_financial_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # عنوان النافذة مع النمط المتطور
        title_label = StyledLabel(f"💰 التفاصيل المالية: {self.supplier.name}", 'hero')
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # وصف توضيحي
        desc_label = StyledLabel("عرض شامل للمعاملات المالية والمصروفات", 'title')
        desc_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(desc_label)

        # إنشاء مقسم عمودي للمحتوى
        from PyQt5.QtWidgets import QSplitter
        splitter = QSplitter(Qt.Vertical)

        # القسم العلوي - الملخص المالي
        top_widget = self.create_financial_summary_section()
        splitter.addWidget(top_widget)

        # القسم السفلي - جدول المصروفات
        bottom_widget = self.create_expenses_table_section()
        splitter.addWidget(bottom_widget)

        # ضبط أحجام المقسم
        splitter.setSizes([300, 500])
        main_layout.addWidget(splitter)

        # أزرار الإغلاق
        button_layout = self.create_button_layout([
            ('close', '❌ إغلاق النافذة', 'secondary', self.accept)
        ])
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def create_financial_summary_section(self):
        """إنشاء قسم الملخص المالي"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة الملخص المالي
        summary_group = StyledGroupBox("📊 الملخص المالي")
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)
        summary_layout.setContentsMargins(20, 25, 20, 25)

        # حساب الإحصائيات المالية
        expenses = self.session.query(Expense).filter_by(supplier_id=self.supplier.id).all()
        total_amount = sum(expense.amount for expense in expenses)

        # حساب المبلغ المدفوع من الملاحظات
        paid_amount = 0
        for expense in expenses:
            if expense.notes and "المبلغ المدفوع:" in expense.notes:
                try:
                    paid_text = expense.notes.split("المبلغ المدفوع:")[1].split("|")[0].strip()
                    paid_amount += float(paid_text)
                except (ValueError, IndexError):
                    pass

        remaining_amount = total_amount - paid_amount

        # تحديد لون المبلغ المتبقي
        remaining_color = 'success' if remaining_amount <= 0 else 'danger'

        # بطاقات الملخص
        total_card = self.create_summary_card("💰 إجمالي المصروفات", f"{total_amount:.2f} ر.س", 'info')
        paid_card = self.create_summary_card("✅ المبلغ المدفوع", f"{paid_amount:.2f} ر.س", 'success')
        remaining_card = self.create_summary_card("⏳ المبلغ المتبقي", f"{remaining_amount:.2f} ر.س", remaining_color)
        count_card = self.create_summary_card("📋 عدد المصروفات", str(len(expenses)), 'normal')

        summary_layout.addWidget(total_card)
        summary_layout.addWidget(paid_card)
        summary_layout.addWidget(remaining_card)
        summary_layout.addWidget(count_card)

        summary_group.setLayout(summary_layout)
        layout.addWidget(summary_group)

        return widget

    def create_summary_card(self, title, value, style_type):
        """إنشاء بطاقة ملخص"""
        card = QWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(15, 15, 15, 15)
        card_layout.setSpacing(10)

        # عنوان البطاقة
        title_label = StyledLabel(title, 'normal')
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)

        # قيمة البطاقة
        value_label = StyledLabel(value, style_type)
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)

        # تطبيق نمط البطاقة
        card.setStyleSheet(f"""
            QWidget {{
                background: {UnifiedStyles.COLORS['surface_glass']};
                border: 2px solid {UnifiedStyles.COLORS['border_accent']};
                border-radius: {UnifiedStyles.BORDER_RADIUS['lg']};


            }}
        """)

        return card

    def create_expenses_table_section(self):
        """إنشاء قسم جدول المصروفات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة جدول المصروفات
        table_group = StyledGroupBox("📋 قائمة المصروفات")
        table_layout = QVBoxLayout()

        # إنشاء الجدول
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setHorizontalHeaderLabels([
            "العنوان", "التاريخ", "تاريخ الاستحقاق",
            "المبلغ الإجمالي", "المبلغ المدفوع", "الحالة"
        ])

        # تطبيق نمط الجدول المتطور
        self.expenses_table.setStyleSheet(UnifiedStyles.get_table_style())

        # إعداد خصائص الجدول
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setSelectionMode(QTableWidget.SingleSelection)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.verticalHeader().setVisible(False)

        # ضبط عرض الأعمدة
        from PyQt5.QtWidgets import QHeaderView
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        table_layout.addWidget(self.expenses_table)
        table_group.setLayout(table_layout)
        layout.addWidget(table_group)

        return widget

    def load_financial_data(self):
        """تحميل البيانات المالية"""
        try:
            # البحث عن مصروفات المورد
            expenses = self.session.query(Expense).filter_by(supplier_id=self.supplier.id).order_by(Expense.date.desc()).all()

            # تحديث الجدول
            self.expenses_table.setRowCount(len(expenses))

            for row, expense in enumerate(expenses):
                # العنوان
                self.expenses_table.setItem(row, 0, QTableWidgetItem(expense.title or ""))

                # التاريخ
                date_str = expense.date.strftime('%Y-%m-%d') if expense.date else ""
                self.expenses_table.setItem(row, 1, QTableWidgetItem(date_str))

                # تاريخ الاستحقاق
                due_date_str = ""
                if expense.notes and "|تاريخ المبلغ المتبقي:" in expense.notes:
                    try:
                        due_date_str = expense.notes.split("|تاريخ المبلغ المتبقي:")[1].strip()
                    except (ValueError, IndexError):
                        pass
                self.expenses_table.setItem(row, 2, QTableWidgetItem(due_date_str))

                # المبلغ الإجمالي
                self.expenses_table.setItem(row, 3, QTableWidgetItem(f"{expense.amount:.2f}"))

                # المبلغ المدفوع
                paid_amount = 0
                if expense.notes and "المبلغ المدفوع:" in expense.notes:
                    try:
                        paid_text = expense.notes.split("المبلغ المدفوع:")[1].split("|")[0].strip()
                        paid_amount = float(paid_text)
                    except (ValueError, IndexError):
                        pass
                self.expenses_table.setItem(row, 4, QTableWidgetItem(f"{paid_amount:.2f}"))

                # الحالة
                status_text = self.get_status_text(expense.category)
                self.expenses_table.setItem(row, 5, QTableWidgetItem(status_text))

        except Exception as e:
            print(f"خطأ في تحميل البيانات المالية: {str(e)}")

    def get_status_text(self, status):
        """تحويل حالة المصروف إلى نص"""
        status_map = {
            'pending': 'قيد الانتظار',
            'paid': 'مدفوع',
            'partially_paid': 'مدفوع جزئياً',
            'cancelled': 'ملغى'
        }
        return status_map.get(status, 'غير محدد')

